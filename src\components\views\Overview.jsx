import { useState, useEffect } from 'react';
import { useCylinder } from '../../contexts/cylinder';
import { useAuth } from '../../contexts/auth';
import { useUI } from '../../contexts/ui';
import { Search, Filter, Plus, Download, Eye, ArrowUpDown } from 'lucide-react';

function Overview() {
  const { cylinders, CYLINDER_STATUS, LOCATIONS } = useCylinder();
  const { user } = useAuth();
  const { addNotification } = useUI();

  // State for cylinder search and filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [filteredCylinders, setFilteredCylinders] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'id', direction: 'ascending' });

  // Count cylinders by status
  const statusCounts = Object.values(CYLINDER_STATUS).reduce((acc, status) => {
    acc[status] = cylinders.filter(c => c.status === status).length;
    return acc;
  }, {});

  // Apply filters and search
  useEffect(() => {
    let result = [...cylinders];

    // Apply search term
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      result = result.filter(cylinder =>
        cylinder.id.toLowerCase().includes(lowerSearchTerm) ||
        cylinder.qrData.toLowerCase().includes(lowerSearchTerm) ||
        cylinder.gasType.toLowerCase().includes(lowerSearchTerm)
      );
    }

    // Apply status filter
    if (statusFilter) {
      result = result.filter(cylinder => cylinder.status === statusFilter);
    }

    // Apply location filter
    if (locationFilter) {
      result = result.filter(cylinder => cylinder.location === locationFilter);
    }

    // Apply sorting
    if (sortConfig.key) {
      result.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredCylinders(result);
  }, [cylinders, searchTerm, statusFilter, locationFilter, sortConfig]);

  // Handle sorting
  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="space-y-6 pb-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Dashboard Overview</h1>
          <p className="text-secondary-600">Welcome, {user?.name} | Role: {user?.role}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="card p-4">
          <h3 className="text-secondary-500 text-sm font-medium mb-1">Total Cylinders</h3>
          <p className="text-2xl font-bold text-secondary-900">{cylinders.length}</p>
        </div>

        {Object.entries(statusCounts).slice(0, 3).map(([status, count]) => (
          <div key={status} className="card p-4">
            <h3 className="text-secondary-500 text-sm font-medium mb-1">{status}</h3>
            <p className="text-2xl font-bold text-secondary-900">{count}</p>
          </div>
        ))}
      </div>

      <div className="card overflow-hidden">
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-secondary-900">Cylinder Inventory</h2>
            <div className="flex gap-2">
              <button className="btn-primary flex items-center gap-2">
                <Plus className="h-4 w-4" />
                <span>Register Now</span>
              </button>
              <button className="btn-secondary flex items-center gap-2">
                <Download className="h-4 w-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 border-b border-secondary-200">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">Filter Cylinders</h3>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-secondary-400" />
              </div>
              <input
                type="text"
                className="input pl-10"
                placeholder="Search by ID, Serial Number, or Product"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="w-full md:w-48">
              <select
                className="select"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">Filter by Status</option>
                {Object.values(CYLINDER_STATUS).map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>

            <div className="w-full md:w-48">
              <select
                className="select"
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
              >
                <option value="">Filter by Location</option>
                {Object.values(LOCATIONS).map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>

            <button className="btn-secondary md:w-auto w-full flex items-center justify-center gap-2">
              <Filter className="h-4 w-4" />
              <span className="md:hidden lg:inline">Advanced Filters</span>
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full min-w-[800px]">
            <thead className="bg-secondary-50 text-secondary-500 text-xs uppercase">
              <tr>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('id')}>
                  <div className="flex items-center gap-2">
                    Cylinder ID
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('qrData')}>
                  <div className="flex items-center gap-2">
                    Serial Number
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('gasType')}>
                  <div className="flex items-center gap-2">
                    Type
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('size')}>
                  <div className="flex items-center gap-2">
                    Capacity
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('status')}>
                  <div className="flex items-center gap-2">
                    Status
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('location')}>
                  <div className="flex items-center gap-2">
                    Location
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('gasType')}>
                  <div className="flex items-center gap-2">
                    Product
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider cursor-pointer" onClick={() => requestSort('createdAt')}>
                  <div className="flex items-center gap-2">
                    Last Updated
                    <ArrowUpDown className="h-4 w-4" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left font-medium tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              {filteredCylinders.length > 0 ? (
                filteredCylinders.map((cylinder) => (
                  <tr key={cylinder.id} className="hover:bg-secondary-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary-900">
                      {cylinder.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      {cylinder.qrData.split('-')[0]}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      Type {cylinder.id.charAt(cylinder.id.length - 1)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      {cylinder.size === 'Small' ? '10L' : cylinder.size === 'Medium' ? '40L' : '80L'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(cylinder.status)}`}>
                        {cylinder.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      {cylinder.location}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      {cylinder.gasType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      {formatDate(cylinder.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      <button className="text-primary-600 hover:text-primary-900 font-medium flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>View</span>
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="px-6 py-4 text-center text-sm text-secondary-500">
                    No cylinders found matching your criteria
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <div className="p-4 border-t border-secondary-200 flex justify-between items-center">
          <div className="text-sm text-secondary-500">
            Showing {filteredCylinders.length} of {cylinders.length} cylinders
          </div>
          <div className="flex gap-2">
            <button className="btn-secondary btn-sm" disabled>
              Previous
            </button>
            <button className="btn-secondary btn-sm" disabled>
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Helper function to get status badge color
  function getStatusBadgeColor(status) {
    switch(status) {
      case CYLINDER_STATUS.NEW:
        return 'bg-blue-100 text-blue-800';
      case CYLINDER_STATUS.IN_PRODUCTION:
        return 'bg-purple-100 text-purple-800';
      case CYLINDER_STATUS.IN_QC:
        return 'bg-yellow-100 text-yellow-800';
      case CYLINDER_STATUS.QC_PASSED:
        return 'bg-green-100 text-green-800';
      case CYLINDER_STATUS.QC_FAILED:
        return 'bg-red-100 text-red-800';
      case CYLINDER_STATUS.READY_FOR_SHIPPING:
        return 'bg-indigo-100 text-indigo-800';
      case CYLINDER_STATUS.SHIPPED:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}

export default Overview;
