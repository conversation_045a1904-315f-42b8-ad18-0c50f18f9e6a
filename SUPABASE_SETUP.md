# Supabase Setup Instructions

## Step 1: Create a Supabase Project

1. Go to [https://supabase.com](https://supabase.com)
2. Click "Start your project" and sign up/login
3. Click "New Project" 
4. Choose your organization
5. Enter project name: `cylinder-management-system`
6. Enter a strong database password
7. Choose a region close to your users
8. Click "Create new project"

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to Settings → API
2. Copy the "Project URL" 
3. Copy the "anon public" key

## Step 3: Setup Environment Variables

1. In your project root, copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=your_project_url_here
   VITE_SUPABASE_ANON_KEY=your_anon_key_here
   ```

## Step 4: Setup Database Schema

1. In your Supabase dashboard, go to SQL Editor
2. Copy and paste the contents of `database/schema.sql`
3. Click "Run" to execute the SQL

This will create:
- `user_profiles` table for user information
- `cylinders` table for cylinder data
- Row Level Security (RLS) policies
- Necessary indexes for performance
- Auto-updating timestamp triggers

## Step 5: Create Your First User

Since Supabase Auth is enabled, you can either:

### Option A: Use Supabase Auth UI (Recommended for production)
```jsx
// Add this to your AuthContext if you want signup functionality
const register = async (email, password, profileData = {}) => {
  const userData = await authApi.register(email, password, profileData);
  return userData;
};
```

### Option B: Create a user manually in Supabase Dashboard
1. Go to Authentication → Users
2. Click "Add user"
3. Enter email and password
4. After creation, go to SQL Editor and run:
   ```sql
   INSERT INTO user_profiles (id, email, name, role)
   VALUES (
     'user_id_from_auth_users_table',
     '<EMAIL>',
     'User Name',
     'Admin'
   );
   ```

## Step 6: Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Try logging in with your created user credentials

## Database Schema Overview

### user_profiles
- `id` (UUID) - References auth.users(id)
- `email` (TEXT) - User email
- `name` (TEXT) - Display name
- `role` (TEXT) - User role (Store, Production, QC, Logistics, Admin)
- `created_at`, `updated_at` - Timestamps

### cylinders
- `id` (TEXT) - Primary key, cylinder identifier
- `type` (TEXT) - Gas type (Oxygen, Nitrogen, etc.)
- `status` (TEXT) - Current status
- `location` (TEXT) - Current location
- `qr_data` (TEXT) - QR code data (unique)
- `specifications` (JSONB) - Technical specs
- `history` (JSONB) - Array of history entries
- `created_at`, `updated_at` - Timestamps

## Security Notes

- Row Level Security (RLS) is enabled
- Users can only view/edit their own profiles
- All authenticated users can view/modify cylinders
- Only Admins can delete cylinders
- The anon key is safe to use in client-side code due to RLS

## Troubleshooting

### Connection Issues
- Verify your environment variables are correct
- Check that your `.env.local` file is in the project root
- Restart your development server after adding environment variables

### Authentication Issues
- Make sure you've created the user_profiles record after creating the auth user
- Check the browser console for any error messages
- Verify your Supabase project is active (not paused)

### Database Issues
- Ensure you've run the schema.sql file
- Check that RLS policies are enabled
- Verify your user has the correct role in user_profiles table
