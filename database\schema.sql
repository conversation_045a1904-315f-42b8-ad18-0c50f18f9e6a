-- Supabase Database Schema for Cylinder Management System

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    role TEXT DEFAULT 'Store' CHECK (role IN ('Store', 'Production', 'QC', 'Logistics', 'Admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create cylinders table
CREATE TABLE IF NOT EXISTS cylinders (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'Available',
    location TEXT NOT NULL DEFAULT 'Store',
    qr_data TEXT UNIQUE,
    specifications JSONB DEFAULT '{}',
    history JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cylinders_status ON cylinders(status);
CREATE INDEX IF NOT EXISTS idx_cylinders_location ON cylinders(location);
CREATE INDEX IF NOT EXISTS idx_cylinders_type ON cylinders(type);
CREATE INDEX IF NOT EXISTS idx_cylinders_qr_data ON cylinders(qr_data);

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cylinders ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for cylinders
CREATE POLICY "Authenticated users can view cylinders" ON cylinders
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert cylinders" ON cylinders
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can update cylinders" ON cylinders
    FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Only admins can delete cylinders" ON cylinders
    FOR DELETE TO authenticated USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role = 'Admin'
        )
    );

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cylinders_updated_at 
    BEFORE UPDATE ON cylinders 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data (optional)
-- You can uncomment these lines to add sample data

-- INSERT INTO cylinders (id, type, status, location, qr_data, specifications) VALUES
-- ('CYL001', 'Oxygen', 'Available', 'Store', 'QR-CYL001', '{"capacity": "10L", "pressure": "150bar", "material": "Steel"}'),
-- ('CYL002', 'Nitrogen', 'In Use', 'Production', 'QR-CYL002', '{"capacity": "20L", "pressure": "200bar", "material": "Aluminum"}'),
-- ('CYL003', 'Argon', 'Maintenance', 'QC', 'QR-CYL003', '{"capacity": "15L", "pressure": "180bar", "material": "Steel"}'),
-- ('CYL004', 'CO2', 'Available', 'Store', 'QR-CYL004', '{"capacity": "25L", "pressure": "220bar", "material": "Steel"}'),
-- ('CYL005', 'Helium', 'In Transit', 'Logistics', 'QR-CYL005', '{"capacity": "12L", "pressure": "160bar", "material": "Aluminum"}');
