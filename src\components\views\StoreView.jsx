import { useState } from 'react';
import ReactDOM from 'react-dom';
import ReactDOMServer from 'react-dom/server';
import { useCylinder } from '../../contexts/cylinder/CylinderContext';
import { useAuth } from '../../contexts/auth/AuthContext';
import { useUI } from '../../contexts/ui/UIContext';
import { QRCodeSVG } from 'qrcode.react';
import { Package, QrCode, Printer, Plus, X } from 'lucide-react';

function StoreView() {
  const { cylinders, addCylinder, addCylindersBatch, CYLINDER_STATUS, LOCATIONS } = useCylinder();
  const { addNotification, showLoading, hideLoading } = useUI();
  const { user } = useAuth();
  const [newCylinder, setNewCylinder] = useState({
    id: '',
    gasType: 'Oxygen',
    size: 'Medium',
    registrationType: 'single',
    material: '',
    tareWeight: '',
    manufactureDate: '',
    hydrostaticTestDate: '',
    initialLocation: 'Store',
    notes: ''
  });

  // State for batch creation
  const [batchData, setBatchData] = useState({
    prefix: 'CYL-',
    startNumber: 1,
    count: 5,
    gasType: 'Oxygen',
    size: 'Medium',
    material: '',
    manufactureDate: '',
    initialLocation: 'Store'
  });

  // State for QR code display
  const [selectedCylinder, setSelectedCylinder] = useState(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [batchCreatedCylinders, setBatchCreatedCylinders] = useState([]);
  const [showBatchQRModal, setShowBatchQRModal] = useState(false);

  // Filter cylinders in the store location
  const storeCylinders = cylinders.filter(c => c.location === LOCATIONS.STORE);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewCylinder(prev => ({ ...prev, [name]: value }));
  };

  const handleBatchInputChange = (e) => {
    const { name, value } = e.target;
    setBatchData(prev => ({
      ...prev,
      [name]: name === 'count' || name === 'startNumber' ? parseInt(value, 10) || 0 : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      showLoading();

      // Transform form data to match API structure
      const cylinderData = {
        id: newCylinder.id,
        type: newCylinder.gasType, // Map gasType to type
        status: CYLINDER_STATUS.AVAILABLE,
        location: LOCATIONS.STORE,
        qrData: `QR-${newCylinder.id}`, // Generate QR data
        specifications: {
          gasType: newCylinder.gasType,
          size: newCylinder.size,
          material: newCylinder.material,
          tareWeight: newCylinder.tareWeight,
          manufactureDate: newCylinder.manufactureDate,
          hydrostaticTestDate: newCylinder.hydrostaticTestDate,
          notes: newCylinder.notes
        },
        history: [{
          timestamp: new Date().toISOString(),
          action: 'Created',
          user: user.name,
          status: CYLINDER_STATUS.AVAILABLE,
          location: LOCATIONS.STORE
        }]
      };

      console.log('Submitting cylinder data:', cylinderData);
      const newCylinderData = await addCylinder(cylinderData);
      console.log('Cylinder created successfully:', newCylinderData);

      // Reset form
      setNewCylinder({
        id: '',
        gasType: 'Oxygen',
        size: 'Medium',
        registrationType: 'single',
        material: '',
        tareWeight: '',
        manufactureDate: '',
        hydrostaticTestDate: '',
        initialLocation: 'Store',
        notes: ''
      });

      // Show QR code
      setSelectedCylinder(newCylinderData);
      setShowQRModal(true);

      // Show success notification
      addNotification(`Cylinder ${newCylinderData.id} registered successfully`, 'success');
    } catch (error) {
      console.error('Error registering cylinder:', error);
      addNotification(`Error registering cylinder: ${error.message}`, 'error');
    } finally {
      hideLoading();
    }
  };

  const handleBatchSubmit = async (e) => {
    e.preventDefault();
    try {
      showLoading();

      // Generate batch cylinder data
      const cylindersData = [];
      for (let i = 0; i < batchData.count; i++) {
        const cylinderId = `${batchData.prefix}${String(batchData.startNumber + i).padStart(3, '0')}`;
        cylindersData.push({
          id: cylinderId,
          type: batchData.gasType,
          status: CYLINDER_STATUS.AVAILABLE,
          location: LOCATIONS.STORE,
          qrData: `QR-${cylinderId}`,
          specifications: {
            gasType: batchData.gasType,
            size: batchData.size,
            material: batchData.material,
            manufactureDate: batchData.manufactureDate
          },
          history: [{
            timestamp: new Date().toISOString(),
            action: 'Created (Batch)',
            user: user.name,
            status: CYLINDER_STATUS.AVAILABLE,
            location: LOCATIONS.STORE
          }]
        });
      }

      console.log('Submitting batch cylinder data:', cylindersData);
      const newCylinders = await addCylindersBatch(cylindersData);
      console.log('Batch cylinders created successfully:', newCylinders);

      // Reset form
      setBatchData({
        prefix: 'CYL-',
        startNumber: 1,
        count: 5,
        gasType: 'Oxygen',
        size: 'Medium',
        material: '',
        manufactureDate: '',
        initialLocation: 'Store'
      });

      // Show batch QR codes
      setBatchCreatedCylinders(newCylinders);
      setShowBatchQRModal(true);

      // Show success notification
      addNotification(`${newCylinders.length} cylinders registered successfully`, 'success');
    } catch (error) {
      console.error('Error registering cylinders:', error);
      addNotification(`Error registering cylinders: ${error.message}`, 'error');
    } finally {
      hideLoading();
    }
  };

  const handleViewQR = (cylinder) => {
    setSelectedCylinder(cylinder);
    setShowQRModal(true);
  };

  const handlePrintQR = () => {
    // Create a canvas element to render the QR code
    const canvas = document.createElement('canvas');
    const qrCodeSize = 200;
    canvas.width = qrCodeSize;
    canvas.height = qrCodeSize;

    // Create a temporary div to render the QR code SVG
    const tempDiv = document.createElement('div');
    document.body.appendChild(tempDiv);

    // Render the QR code to the temporary div
    const qrCodeElement = <QRCodeSVG value={selectedCylinder.qrData} size={qrCodeSize} />;
    ReactDOM.render(qrCodeElement, tempDiv);

    // Wait a moment for the SVG to render
    setTimeout(() => {
      // Use html2canvas to convert the SVG to a canvas
      const svgElement = tempDiv.querySelector('svg');

      // Create a data URL from the SVG
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Create an image from the SVG URL
      const img = new Image();
      img.onload = () => {
        // Draw the image on the canvas
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, qrCodeSize, qrCodeSize);

        // Convert canvas to data URL
        const qrCodeDataUrl = canvas.toDataURL('image/png');

        // Clean up
        URL.revokeObjectURL(svgUrl);
        document.body.removeChild(tempDiv);

        // Create and open the print window
        const printWindow = window.open('', '_blank');
        const currentDate = new Date().toLocaleString();

        printWindow.document.write(`
          <html>
            <head>
              <title>Print QR Code</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .header { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 12px; color: #666; }
                .qr-container { text-align: center; margin: 20px auto; max-width: 500px; }
                .qr-code { margin-bottom: 15px; }
                .qr-info { margin-top: 10px; text-align: center; }
                .cylinder-id { font-size: 24px; font-weight: bold; margin: 5px 0; }
                .qr-data { font-size: 14px; color: #666; margin-bottom: 20px; }
                .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                .details-table th, .details-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .details-table th { background-color: #f2f2f2; width: 40%; }
              </style>
            </head>
            <body>
              <div class="header">
                <div>${currentDate}</div>
                <div>Print QR Code</div>
              </div>

              <div class="qr-container">
                <div class="qr-code">
                  <img src="${qrCodeDataUrl}" alt="QR Code" style="width: 200px; height: 200px;" />
                </div>

                <div class="qr-info">
                  <div class="cylinder-id">${selectedCylinder.id}</div>
                  <div class="qr-data">${selectedCylinder.qrData}</div>
                </div>

                <table class="details-table">
                  <tr>
                    <th>Serial Number</th>
                    <td>${selectedCylinder.id}</td>
                  </tr>
                  <tr>
                    <th>Cylinder Type</th>
                    <td>${selectedCylinder.gasType}</td>
                  </tr>
                  <tr>
                    <th>Capacity</th>
                    <td>${selectedCylinder.size}</td>
                  </tr>
                  ${selectedCylinder.material ? `
                  <tr>
                    <th>Material</th>
                    <td>${selectedCylinder.material}</td>
                  </tr>` : ''}
                  ${selectedCylinder.tareWeight ? `
                  <tr>
                    <th>Tare Weight</th>
                    <td>${selectedCylinder.tareWeight} kg</td>
                  </tr>` : ''}
                  ${selectedCylinder.manufactureDate ? `
                  <tr>
                    <th>Manufacture Date</th>
                    <td>${new Date(selectedCylinder.manufactureDate).toLocaleDateString()}</td>
                  </tr>` : ''}
                  ${selectedCylinder.hydrostaticTestDate ? `
                  <tr>
                    <th>Hydrostatic Test Date</th>
                    <td>${new Date(selectedCylinder.hydrostaticTestDate).toLocaleDateString()}</td>
                  </tr>` : ''}
                  <tr>
                    <th>Status</th>
                    <td>${selectedCylinder.status}</td>
                  </tr>
                  <tr>
                    <th>Location</th>
                    <td>${selectedCylinder.location}</td>
                  </tr>
                  <tr>
                    <th>Registration Date</th>
                    <td>${new Date(selectedCylinder.createdAt).toLocaleDateString()}</td>
                  </tr>
                  ${selectedCylinder.notes ? `
                  <tr>
                    <th>Notes</th>
                    <td>${selectedCylinder.notes}</td>
                  </tr>` : ''}
                </table>
              </div>
            </body>
          </html>
        `);

        printWindow.document.close();
        printWindow.focus();

        // Slight delay to ensure content is loaded before printing
        setTimeout(() => {
          printWindow.print();
        }, 500);
      };

      img.src = svgUrl;
    }, 100);
  };

  const handlePrintBatchQR = () => {
    // Create a print window first
    const printWindow = window.open('', '_blank');
    const currentDate = new Date().toLocaleString();

    // Start writing the HTML structure
    printWindow.document.write(`
      <html>
        <head>
          <title>Print Batch QR Codes</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .header { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 12px; color: #666; }
            .qr-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
            .qr-container { text-align: center; padding: 15px; page-break-inside: avoid; border: 1px solid #ddd; border-radius: 5px; }
            .qr-info { margin-top: 10px; font-size: 12px; }
            .cylinder-id { font-weight: bold; font-size: 16px; margin-bottom: 4px; }
            .qr-data { font-family: monospace; font-size: 10px; color: #666; margin-bottom: 10px; }
            .details-table { width: 100%; border-collapse: collapse; margin-top: 10px; font-size: 11px; }
            .details-table th, .details-table td { border: 1px solid #ddd; padding: 5px; text-align: left; }
            .details-table th { background-color: #f2f2f2; width: 40%; }
            @media print {
              .page-break { page-break-after: always; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>${currentDate}</div>
            <div>Print Batch QR Codes</div>
          </div>

          <div class="qr-grid" id="qr-grid-container">
            <!-- QR codes will be inserted here -->
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Function to convert SVG to image and add to print window
    const processQRCodes = async () => {
      const qrGridContainer = printWindow.document.getElementById('qr-grid-container');

      // Process each cylinder
      for (let i = 0; i < batchCreatedCylinders.length; i++) {
        const cylinder = batchCreatedCylinders[i];

        // Create a container for this cylinder's QR code
        const containerDiv = printWindow.document.createElement('div');
        containerDiv.className = `qr-container ${i > 0 && i % 2 === 0 ? 'page-break' : ''}`;

        // Create a temporary div to render the QR code SVG
        const tempDiv = document.createElement('div');
        document.body.appendChild(tempDiv);

        // Render the QR code to the temporary div
        const qrCodeElement = <QRCodeSVG value={cylinder.qrData} size={150} />;
        ReactDOM.render(qrCodeElement, tempDiv);

        // Wait for the SVG to render
        await new Promise(resolve => setTimeout(resolve, 50));

        // Get the SVG element
        const svgElement = tempDiv.querySelector('svg');

        // Create a data URL from the SVG
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const svgUrl = URL.createObjectURL(svgBlob);

        // Create a canvas to render the image
        const canvas = document.createElement('canvas');
        const qrCodeSize = 150;
        canvas.width = qrCodeSize;
        canvas.height = qrCodeSize;

        // Create an image from the SVG URL
        const img = new Image();

        // Wait for the image to load
        await new Promise(resolve => {
          img.onload = resolve;
          img.src = svgUrl;
        });

        // Draw the image on the canvas
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, qrCodeSize, qrCodeSize);

        // Convert canvas to data URL
        const qrCodeDataUrl = canvas.toDataURL('image/png');

        // Clean up
        URL.revokeObjectURL(svgUrl);
        document.body.removeChild(tempDiv);

        // Create the HTML content for this cylinder
        containerDiv.innerHTML = `
          <img src="${qrCodeDataUrl}" alt="QR Code" style="width: 150px; height: 150px;" />
          <div class="qr-info">
            <div class="cylinder-id">${cylinder.id}</div>
            <div class="qr-data">${cylinder.qrData}</div>

            <table class="details-table">
              <tr>
                <th>Cylinder Type</th>
                <td>${cylinder.gasType}</td>
              </tr>
              <tr>
                <th>Capacity</th>
                <td>${cylinder.size}</td>
              </tr>
              ${cylinder.material ? `
              <tr>
                <th>Material</th>
                <td>${cylinder.material}</td>
              </tr>` : ''}
              ${cylinder.manufactureDate ? `
              <tr>
                <th>Manufacture Date</th>
                <td>${new Date(cylinder.manufactureDate).toLocaleDateString()}</td>
              </tr>` : ''}
              <tr>
                <th>Registration Date</th>
                <td>${new Date(cylinder.createdAt).toLocaleDateString()}</td>
              </tr>
            </table>
          </div>
        `;

        // Add this container to the grid
        qrGridContainer.appendChild(containerDiv);
      }

      // Focus and print after all QR codes are processed
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // Start processing QR codes
    processQRCodes().catch(error => {
      console.error('Error processing QR codes:', error);
      addNotification('Error preparing QR codes for printing', 'error');
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Store Management</h1>
          <p className="text-secondary-600">Add new cylinders and generate QR codes</p>
        </div>
      </div>

      <div className="card p-6 overflow-visible">
        <h2 className="text-2xl font-bold mb-2">Cylinder Registration</h2>
        <p className="text-secondary-600 mb-6">Enter the details for the new cylinder to register it in the system</p>

        {/* Tab Navigation */}
        <div className="flex mb-6 border-b overflow-x-auto pb-1 -mx-1 px-1">
          <button
            className={`py-3 px-6 font-medium whitespace-nowrap ${newCylinder.registrationType === 'single' ? 'border-b-2 border-primary-600 text-primary-600' : 'text-secondary-500'}`}
            onClick={() => setNewCylinder(prev => ({ ...prev, registrationType: 'single' }))}
          >
            Single Cylinder
          </button>
          <button
            className={`py-3 px-6 font-medium whitespace-nowrap ${newCylinder.registrationType === 'batch' ? 'border-b-2 border-primary-600 text-primary-600' : 'text-secondary-500'}`}
            onClick={() => setNewCylinder(prev => ({ ...prev, registrationType: 'batch' }))}
          >
            Batch Registration
          </button>
        </div>

        {newCylinder.registrationType === 'single' ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label" htmlFor="serialNumber">
                  Serial Number
                </label>
                <input
                  id="serialNumber"
                  name="id"
                  type="text"
                  className="input"
                  value={newCylinder.id}
                  onChange={handleInputChange}
                  placeholder="Enter serial number"
                  required
                />
              </div>

              <div>
                <label className="label" htmlFor="cylinderType">
                  Cylinder Type
                </label>
                <select
                  id="cylinderType"
                  name="gasType"
                  className="select"
                  value={newCylinder.gasType}
                  onChange={handleInputChange}
                  required
                >
                  <option value="" disabled>Select type</option>
                  <option value="Oxygen">Oxygen</option>
                  <option value="Nitrogen">Nitrogen</option>
                  <option value="Helium">Helium</option>
                  <option value="Carbon Dioxide">Carbon Dioxide</option>
                  <option value="Argon">Argon</option>
                </select>
              </div>

              <div>
                <label className="label" htmlFor="capacity">
                  Capacity
                </label>
                <select
                  id="capacity"
                  name="size"
                  className="select"
                  value={newCylinder.size}
                  onChange={handleInputChange}
                  required
                >
                  <option value="" disabled>Select capacity</option>
                  <option value="Small">Small (10L)</option>
                  <option value="Medium">Medium (40L)</option>
                  <option value="Large">Large (80L)</option>
                </select>
              </div>

              <div>
                <label className="label" htmlFor="material">
                  Material
                </label>
                <select
                  id="material"
                  name="material"
                  className="select"
                  value={newCylinder.material || ''}
                  onChange={handleInputChange}
                >
                  <option value="" disabled>Select material</option>
                  <option value="Steel">Steel</option>
                  <option value="Aluminum">Aluminum</option>
                  <option value="Composite">Composite</option>
                </select>
              </div>

              <div>
                <label className="label" htmlFor="tareWeight">
                  Tare Weight (kg)
                </label>
                <input
                  id="tareWeight"
                  name="tareWeight"
                  type="text"
                  className="input"
                  value={newCylinder.tareWeight || ''}
                  onChange={handleInputChange}
                  placeholder="Enter tare weight"
                />
              </div>

              <div>
                <label className="label" htmlFor="manufactureDate">
                  Manufacture Date
                </label>
                <input
                  id="manufactureDate"
                  name="manufactureDate"
                  type="date"
                  className="input"
                  value={newCylinder.manufactureDate || ''}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="label" htmlFor="hydrostaticTestDate">
                  Hydrostatic Test Date
                </label>
                <input
                  id="hydrostaticTestDate"
                  name="hydrostaticTestDate"
                  type="date"
                  className="input"
                  value={newCylinder.hydrostaticTestDate || ''}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="label" htmlFor="initialLocation">
                  Initial Location
                </label>
                <select
                  id="initialLocation"
                  name="initialLocation"
                  className="select"
                  value={newCylinder.initialLocation || 'Store'}
                  onChange={handleInputChange}
                >
                  <option value="" disabled>Select location</option>
                  <option value="Store">Store</option>
                  <option value="Production">Production</option>
                  <option value="QC">Quality Control</option>
                  <option value="Logistics">Logistics</option>
                </select>
              </div>
            </div>

            <div>
              <label className="label" htmlFor="notes">
                Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                className="input min-h-[100px]"
                value={newCylinder.notes || ''}
                onChange={handleInputChange}
                placeholder="Enter any additional notes"
              ></textarea>
            </div>

            <button
              type="submit"
              className="btn-primary w-full py-3 text-base font-medium"
            >
              Register Cylinder
            </button>
          </form>
        ) : (
          <form onSubmit={handleBatchSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label" htmlFor="prefix">
                  Serial Number Prefix
                </label>
                <input
                  id="prefix"
                  name="prefix"
                  type="text"
                  className="input"
                  value={batchData.prefix}
                  onChange={handleBatchInputChange}
                  placeholder="e.g. CYL-"
                  required
                />
              </div>

              <div>
                <label className="label" htmlFor="startNumber">
                  Start Number
                </label>
                <input
                  id="startNumber"
                  name="startNumber"
                  type="number"
                  min="1"
                  className="input"
                  value={batchData.startNumber}
                  onChange={handleBatchInputChange}
                  required
                />
              </div>

              <div>
                <label className="label" htmlFor="count">
                  Number of Cylinders
                </label>
                <input
                  id="count"
                  name="count"
                  type="number"
                  min="1"
                  max="50"
                  className="input"
                  value={batchData.count}
                  onChange={handleBatchInputChange}
                  required
                />
              </div>

              <div>
                <label className="label" htmlFor="batchCylinderType">
                  Cylinder Type
                </label>
                <select
                  id="batchCylinderType"
                  name="gasType"
                  className="select"
                  value={batchData.gasType}
                  onChange={handleBatchInputChange}
                  required
                >
                  <option value="" disabled>Select type</option>
                  <option value="Oxygen">Oxygen</option>
                  <option value="Nitrogen">Nitrogen</option>
                  <option value="Helium">Helium</option>
                  <option value="Carbon Dioxide">Carbon Dioxide</option>
                  <option value="Argon">Argon</option>
                </select>
              </div>

              <div>
                <label className="label" htmlFor="batchCapacity">
                  Capacity
                </label>
                <select
                  id="batchCapacity"
                  name="size"
                  className="select"
                  value={batchData.size}
                  onChange={handleBatchInputChange}
                  required
                >
                  <option value="" disabled>Select capacity</option>
                  <option value="Small">Small (10L)</option>
                  <option value="Medium">Medium (40L)</option>
                  <option value="Large">Large (80L)</option>
                </select>
              </div>

              <div>
                <label className="label" htmlFor="batchMaterial">
                  Material
                </label>
                <select
                  id="batchMaterial"
                  name="material"
                  className="select"
                  value={batchData.material || ''}
                  onChange={handleBatchInputChange}
                >
                  <option value="" disabled>Select material</option>
                  <option value="Steel">Steel</option>
                  <option value="Aluminum">Aluminum</option>
                  <option value="Composite">Composite</option>
                </select>
              </div>

              <div>
                <label className="label" htmlFor="batchManufactureDate">
                  Manufacture Date
                </label>
                <input
                  id="batchManufactureDate"
                  name="manufactureDate"
                  type="date"
                  className="input"
                  value={batchData.manufactureDate || ''}
                  onChange={handleBatchInputChange}
                />
              </div>

              <div>
                <label className="label" htmlFor="batchInitialLocation">
                  Initial Location
                </label>
                <select
                  id="batchInitialLocation"
                  name="initialLocation"
                  className="select"
                  value={batchData.initialLocation || 'Store'}
                  onChange={handleBatchInputChange}
                >
                  <option value="" disabled>Select location</option>
                  <option value="Store">Store</option>
                  <option value="Production">Production</option>
                  <option value="QC">Quality Control</option>
                  <option value="Logistics">Logistics</option>
                </select>
              </div>
            </div>

            <button
              type="submit"
              className="btn-primary w-full py-3 text-base font-medium"
            >
              Register Batch Cylinders
            </button>
          </form>
        )}
      </div>

      <div className="card overflow-hidden">
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center gap-3">
            <div className="bg-primary-100 p-2 rounded-lg">
              <Package className="h-6 w-6 text-primary-600" />
            </div>
            <h2 className="text-xl font-semibold text-secondary-900">Store Inventory</h2>
          </div>
        </div>

        {storeCylinders.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-secondary-600">No cylinders in store inventory.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-secondary-200">
              <thead className="bg-secondary-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Gas Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Size</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-secondary-200">
                {storeCylinders.map((cylinder) => (
                  <tr key={cylinder.id} className="hover:bg-secondary-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary-900">
                      {cylinder.id}
                      <div className="text-xs text-secondary-500 mt-1">
                        {cylinder.qrData}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">{cylinder.gasType}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">{cylinder.size}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {cylinder.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                      <button
                        onClick={() => handleViewQR(cylinder)}
                        className="btn-sm btn-primary flex items-center gap-1"
                      >
                        <QrCode className="h-3 w-3" />
                        <span>View QR</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* QR Code Modal */}
      {showQRModal && selectedCylinder && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-xl w-full overflow-hidden">
            <div className="bg-primary-600 text-white px-6 py-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Cylinder QR Code</h2>
              <button
                onClick={() => setShowQRModal(false)}
                className="text-white hover:text-primary-100 focus:outline-none"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-6 mb-6">
                <div className="flex flex-col items-center bg-secondary-50 p-6 rounded-lg border border-secondary-200">
                  <QRCodeSVG value={selectedCylinder.qrData} size={200} className="mb-4" />
                  <div className="text-center">
                    <p className="font-medium text-primary-700">{selectedCylinder.id}</p>
                    <p className="text-xs text-secondary-500 font-mono">{selectedCylinder.qrData}</p>
                  </div>
                </div>

                <div className="flex-1 bg-white p-4 rounded border border-secondary-200">
                  <h3 className="font-medium text-lg mb-3 text-secondary-900">Cylinder Details</h3>
                  <div className="grid grid-cols-2 gap-y-3 gap-x-4 text-sm">
                    <div className="font-medium text-secondary-700">Serial Number:</div>
                    <div>{selectedCylinder.id}</div>

                    <div className="font-medium text-secondary-700">Cylinder Type:</div>
                    <div>{selectedCylinder.type || selectedCylinder.specifications?.gasType}</div>

                    <div className="font-medium text-secondary-700">Capacity:</div>
                    <div>{selectedCylinder.specifications?.size || 'N/A'}</div>

                    {selectedCylinder.specifications?.material && (
                      <>
                        <div className="font-medium text-secondary-700">Material:</div>
                        <div>{selectedCylinder.specifications.material}</div>
                      </>
                    )}

                    {selectedCylinder.specifications?.tareWeight && (
                      <>
                        <div className="font-medium text-secondary-700">Tare Weight:</div>
                        <div>{selectedCylinder.specifications.tareWeight} kg</div>
                      </>
                    )}

                    {selectedCylinder.specifications?.manufactureDate && (
                      <>
                        <div className="font-medium text-secondary-700">Manufacture Date:</div>
                        <div>{new Date(selectedCylinder.specifications.manufactureDate).toLocaleDateString()}</div>
                      </>
                    )}

                    {selectedCylinder.specifications?.hydrostaticTestDate && (
                      <>
                        <div className="font-medium text-secondary-700">Hydrostatic Test Date:</div>
                        <div>{new Date(selectedCylinder.specifications.hydrostaticTestDate).toLocaleDateString()}</div>
                      </>
                    )}

                    <div className="font-medium text-secondary-700">Status:</div>
                    <div>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {selectedCylinder.status}
                      </span>
                    </div>

                    <div className="font-medium text-secondary-700">Location:</div>
                    <div>{selectedCylinder.location}</div>

                    <div className="font-medium text-secondary-700">Registration Date:</div>
                    <div>{selectedCylinder.created_at ? new Date(selectedCylinder.created_at).toLocaleDateString() : 'N/A'}</div>
                  </div>

                  {selectedCylinder.specifications?.notes && (
                    <div className="mt-4 border-t pt-3">
                      <div className="font-medium text-secondary-700 mb-1">Notes:</div>
                      <p className="text-sm text-secondary-600">{selectedCylinder.specifications.notes}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  onClick={handlePrintQR}
                  className="btn-primary flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  <span>Print QR Code</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Batch QR Code Modal */}
      {showBatchQRModal && batchCreatedCylinders.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-5xl w-full max-h-[80vh] overflow-hidden">
            <div className="bg-primary-600 text-white px-6 py-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Batch QR Codes</h2>
              <button
                onClick={() => setShowBatchQRModal(false)}
                className="text-white hover:text-primary-100 focus:outline-none"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6 overflow-y-auto">
              <div className="mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-secondary-900">Successfully Registered Cylinders</h3>
                  <span className="bg-primary-100 text-primary-800 text-sm font-medium px-3 py-1 rounded-full">
                    {batchCreatedCylinders.length} cylinders
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {batchCreatedCylinders.map((cylinder) => (
                    <div key={cylinder.id} className="card p-4 flex flex-col md:flex-row gap-4 hover:shadow-card-hover transition-all duration-200">
                      <div className="flex-shrink-0 flex items-center justify-center bg-secondary-50 p-3 rounded-lg border border-secondary-200">
                        <QRCodeSVG value={cylinder.qrData} size={100} />
                      </div>

                      <div className="flex-1">
                        <h4 className="font-medium text-secondary-900 mb-1">{cylinder.id}</h4>
                        <p className="text-secondary-500 font-mono text-xs mb-3 truncate">{cylinder.qrData}</p>

                        <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                          <div className="text-secondary-500">Type:</div>
                          <div className="font-medium">{cylinder.type || cylinder.specifications?.gasType}</div>

                          <div className="text-secondary-500">Capacity:</div>
                          <div className="font-medium">{cylinder.specifications?.size || 'N/A'}</div>

                          {cylinder.specifications?.material && (
                            <>
                              <div className="text-secondary-500">Material:</div>
                              <div className="font-medium">{cylinder.specifications.material}</div>
                            </>
                          )}

                          <div className="text-secondary-500">Status:</div>
                          <div>
                            <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                              {cylinder.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  onClick={handlePrintBatchQR}
                  className="btn-primary flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  <span>Print All QR Codes</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default StoreView;
