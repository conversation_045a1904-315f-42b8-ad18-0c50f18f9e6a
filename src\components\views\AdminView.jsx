import { useState, useEffect } from 'react';
import { useCylinder } from '../../contexts/cylinder';
import { useUI } from '../../contexts/ui';
import { useAuth } from '../../contexts/auth';
import { UserPlus, UserCog, Key, Save, X } from 'lucide-react';

function AdminView() {
  const { cylinders, CYLINDER_STATUS, LOCATIONS } = useCylinder();
  const { addNotification, showLoading, hideLoading } = useUI();
  const { ROLES } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isEditingUser, setIsEditingUser] = useState(false);
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    name: '',
    role: 'Store'
  });

  // Load users on component mount
  useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use the sample users from localStorage or create default ones
    const loadUsers = () => {
      try {
        const storedUsers = localStorage.getItem('users');
        if (storedUsers) {
          setUsers(JSON.parse(storedUsers));
        } else {
          // Default sample users
          const defaultUsers = [
            { id: 1, username: 'store_user', password: 'password', name: 'Store User', role: 'Store' },
            { id: 2, username: 'production_user', password: 'password', name: 'Production User', role: 'Production' },
            { id: 3, username: 'qc_user', password: 'password', name: 'QC User', role: 'QC' },
            { id: 4, username: 'logistics_user', password: 'password', name: 'Logistics User', role: 'Logistics' },
            { id: 5, username: 'admin_user', password: 'password', name: 'Admin User', role: 'Admin' },
          ];
          setUsers(defaultUsers);
          localStorage.setItem('users', JSON.stringify(defaultUsers));
        }
      } catch (error) {
        console.error('Error loading users:', error);
        addNotification('Error loading users', 'error');
      }
    };

    loadUsers();
  }, [addNotification]);

  // Handle adding a new user
  const handleAddUser = () => {
    try {
      showLoading();
      
      // Validate form
      if (!newUser.username || !newUser.password || !newUser.name || !newUser.role) {
        addNotification('Please fill in all fields', 'error');
        return;
      }
      
      // Check if username already exists
      if (users.some(user => user.username === newUser.username)) {
        addNotification('Username already exists', 'error');
        return;
      }
      
      // Create new user
      const newUserWithId = {
        ...newUser,
        id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1
      };
      
      const updatedUsers = [...users, newUserWithId];
      setUsers(updatedUsers);
      localStorage.setItem('users', JSON.stringify(updatedUsers));
      
      // Reset form
      setNewUser({
        username: '',
        password: '',
        name: '',
        role: 'Store'
      });
      
      addNotification(`User ${newUserWithId.name} added successfully`, 'success');
    } catch (error) {
      console.error('Error adding user:', error);
      addNotification('Error adding user', 'error');
    } finally {
      hideLoading();
    }
  };

  // Handle updating a user
  const handleUpdateUser = () => {
    try {
      showLoading();
      
      if (!selectedUser) return;
      
      // Validate form
      if (!selectedUser.username || !selectedUser.name || !selectedUser.role) {
        addNotification('Please fill in all required fields', 'error');
        return;
      }
      
      // Check if username already exists (except for the current user)
      if (users.some(user => user.username === selectedUser.username && user.id !== selectedUser.id)) {
        addNotification('Username already exists', 'error');
        return;
      }
      
      // Update user
      const updatedUsers = users.map(user => 
        user.id === selectedUser.id ? selectedUser : user
      );
      
      setUsers(updatedUsers);
      localStorage.setItem('users', JSON.stringify(updatedUsers));
      setIsEditingUser(false);
      
      addNotification(`User ${selectedUser.name} updated successfully`, 'success');
    } catch (error) {
      console.error('Error updating user:', error);
      addNotification('Error updating user', 'error');
    } finally {
      hideLoading();
    }
  };

  // Handle resetting a user's password
  const handleResetPassword = (user) => {
    try {
      showLoading();
      
      // Reset password to default
      const updatedUser = { ...user, password: 'password' };
      
      // Update user in the list
      const updatedUsers = users.map(u => 
        u.id === user.id ? updatedUser : u
      );
      
      setUsers(updatedUsers);
      localStorage.setItem('users', JSON.stringify(updatedUsers));
      
      // If this is the selected user, update it
      if (selectedUser && selectedUser.id === user.id) {
        setSelectedUser(updatedUser);
      }
      
      addNotification(`Password reset for ${user.name}`, 'success');
    } catch (error) {
      console.error('Error resetting password:', error);
      addNotification('Error resetting password', 'error');
    } finally {
      hideLoading();
    }
  };

  // Handle editing a user
  const handleEditUser = (user) => {
    setSelectedUser({ ...user });
    setIsEditingUser(true);
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setIsEditingUser(false);
    setSelectedUser(null);
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
      
      {/* Tab Navigation */}
      <div className="flex border-b mb-6 overflow-x-auto">
        <button
          className={`py-2 px-4 font-medium ${activeTab === 'dashboard' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-secondary-500 hover:text-secondary-700'}`}
          onClick={() => setActiveTab('dashboard')}
        >
          Dashboard
        </button>
        <button
          className={`py-2 px-4 font-medium ${activeTab === 'users' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-secondary-500 hover:text-secondary-700'}`}
          onClick={() => setActiveTab('users')}
        >
          User Management
        </button>
      </div>
      
      {activeTab === 'dashboard' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Status Overview */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Cylinder Status Overview</h2>
            <div className="space-y-4">
              {Object.entries(CYLINDER_STATUS).map(([key, status]) => (
                <div key={key} className="flex justify-between items-center">
                  <span>{status}</span>
                  <span className="font-semibold">{cylinders.filter(c => c.status === status).length}</span>
                </div>
              ))}
            </div>
          </div>
          
          {/* Location Overview */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Cylinder Location Overview</h2>
            <div className="space-y-4">
              {Object.entries(LOCATIONS).map(([key, location]) => (
                <div key={key} className="flex justify-between items-center">
                  <span>{location}</span>
                  <span className="font-semibold">{cylinders.filter(c => c.location === location).length}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      
      {activeTab === 'users' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User List */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow overflow-hidden">
            <div className="p-4 border-b bg-secondary-50 flex justify-between items-center">
              <h2 className="text-lg font-semibold">User Management</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="bg-secondary-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Name</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Username</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Role</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {users.map(user => (
                    <tr key={user.id} className="hover:bg-secondary-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-secondary-900">{user.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-500">{user.username}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 text-primary-800">
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500 space-x-2">
                        <button 
                          onClick={() => handleEditUser(user)}
                          className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                        >
                          <UserCog className="h-4 w-4 mr-1" /> Edit
                        </button>
                        <button 
                          onClick={() => handleResetPassword(user)}
                          className="text-yellow-600 hover:text-yellow-900 inline-flex items-center ml-2"
                        >
                          <Key className="h-4 w-4 mr-1" /> Reset Password
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Add/Edit User Form */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="p-4 border-b bg-secondary-50">
              <h2 className="text-lg font-semibold">
                {isEditingUser ? 'Edit User' : 'Add New User'}
              </h2>
            </div>
            
            <div className="p-4">
              {isEditingUser ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Username</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={selectedUser?.username || ''}
                      onChange={(e) => setSelectedUser({...selectedUser, username: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Name</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={selectedUser?.name || ''}
                      onChange={(e) => setSelectedUser({...selectedUser, name: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Role</label>
                    <select
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={selectedUser?.role || ''}
                      onChange={(e) => setSelectedUser({...selectedUser, role: e.target.value})}
                    >
                      {Object.values(ROLES).map(role => (
                        <option key={role} value={role}>{role}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="pt-2 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 border border-secondary-300 shadow-sm text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      onClick={handleCancelEdit}
                    >
                      <X className="h-4 w-4 mr-1" /> Cancel
                    </button>
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      onClick={handleUpdateUser}
                    >
                      <Save className="h-4 w-4 mr-1" /> Save Changes
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Username</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={newUser.username}
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                      placeholder="username"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Password</label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      placeholder="password"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Name</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      placeholder="Full Name"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">Role</label>
                    <select
                      className="w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={newUser.role}
                      onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                    >
                      {Object.values(ROLES).map(role => (
                        <option key={role} value={role}>{role}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="pt-2">
                    <button
                      type="button"
                      className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      onClick={handleAddUser}
                    >
                      <UserPlus className="h-4 w-4 mr-1" /> Add User
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminView;
