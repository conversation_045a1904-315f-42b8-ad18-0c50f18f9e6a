import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../contexts/auth/AuthContext';
import { useUI } from '../../contexts/ui/UIContext';
import { QrCode, User, Lock } from 'lucide-react';

function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const { addNotification } = useUI();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await login(email, password);
      navigate('/dashboard');
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row overflow-hidden">
      {/* Left panel - Logo and info */}
      <div className="bg-primary-800 text-white md:w-1/2 p-8 flex flex-col justify-center items-center">
        <div className="max-w-md mx-auto text-center">
          <div className="mb-12 flex flex-col items-center">
            <div className="mb-8">
              <img
                src="/src/assets/ags-logo.png"
                alt="Alpha Gas Solution"
                className="h-20 w-auto"
              />
            </div>
            <div className="text-center">
              <h1 className="text-2xl font-semibold mb-1">Cylinder Management</h1>
              <p className="text-sm font-light text-primary-200 mb-6">Alpha Gas Solution</p>
              <div className="w-24 h-0.5 bg-primary-500 mx-auto mb-6"></div>
              <p className="text-primary-100 max-w-md text-center text-sm leading-relaxed">
                Track cylinders throughout their lifecycle with QR code scanning technology
              </p>
            </div>
          </div>

          <div className="bg-primary-700/50 p-6 rounded-lg">
            <h2 className="font-semibold mb-2 text-xl">System Features</h2>
            <ul className="space-y-2 text-left">
              <li className="flex items-center"><span className="mr-2 bg-primary-600 rounded-full p-1">✓</span> QR Code Generation</li>
              <li className="flex items-center"><span className="mr-2 bg-primary-600 rounded-full p-1">✓</span> Cylinder Tracking</li>
              <li className="flex items-center"><span className="mr-2 bg-primary-600 rounded-full p-1">✓</span> Quality Control</li>
              <li className="flex items-center"><span className="mr-2 bg-primary-600 rounded-full p-1">✓</span> Logistics Management</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Right panel - Login form */}
      <div className="bg-white md:w-1/2 p-8 flex items-center justify-center">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-secondary-900">Welcome Back</h2>
            <p className="text-secondary-600">Please sign in to your account</p>
          </div>

          {error && (
            <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
              <p className="font-medium">Login Error</p>
              <p>{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="label" htmlFor="email">
                Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-secondary-400" />
                </div>
                <input
                  id="email"
                  type="email"
                  className="input pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <div>
              <label className="label" htmlFor="password">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-secondary-400" />
                </div>
                <input
                  id="password"
                  type="password"
                  className="input pl-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              className="btn-primary w-full py-3"
              disabled={isLoading}
            >
              {isLoading ? 'Signing in...' : 'Sign In'}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-secondary-600">
              Don&apos;t have an account?{' '}
              {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
              <Link to="/register" className="font-medium text-primary-600 hover:text-primary-500 ml-1">
                Sign Up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LoginScreen;
