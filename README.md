# Cylinder Management System

A modern web application for tracking gas cylinders throughout their lifecycle using QR code scanning technology.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Supabase account

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cylinder-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup Supabase** (Required for database functionality)
   - Follow the detailed instructions in [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)
   - Create a Supabase project
   - Update `.env.local` with your Supabase credentials

4. **Start development server**
   ```bash
   npm run dev
   ```

## 🗄️ Database Integration

The application now uses **Supabase** as the backend database instead of localStorage. This provides:

- **Real-time data synchronization**
- **User authentication with Supabase Auth**
- **Row Level Security (RLS)** for data protection
- **Scalable PostgreSQL database**
- **Automatic backups and hosting**

### Database Schema
- `user_profiles` - User information and roles
- `cylinders` - Cylinder data with full history tracking

See [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) for complete setup instructions.

## Features

- QR code generation for individual and batch cylinders
- QR code scanning for tracking cylinders across departments
- Responsive design for desktop and mobile devices
- Role-based access control
- Real-time status updates
- Comprehensive cylinder history tracking

## Architecture

The application is built with a modern React architecture focusing on maintainability, performance, and scalability:

### State Management

- **Context API**: Split into domain-specific contexts:
  - `CylinderContext`: Manages cylinder data and operations
  - `AuthContext`: Handles authentication and user management
  - `UIContext`: Controls UI state like notifications and loading indicators

### Code Organization

- **Components**: Organized by feature and responsibility
  - `common/`: Reusable UI components
  - `layout/`: Layout components like DashboardLayout
  - `views/`: Main view components for each route
  - `auth/`: Authentication-related components

- **Contexts**: Domain-specific state management
  - `cylinder/`: Cylinder-related state and operations
  - `auth/`: Authentication state and operations
  - `ui/`: UI-related state and operations

- **Hooks**: Custom React hooks
  - `useCylinderData`: Data operations with loading and error handling
  - `useLocalStorage`: Persistent state in localStorage
  - `useDebounce`: Debounced values for search inputs
  - `useMediaQuery`: Responsive design helpers

- **Services**: API abstraction layer
  - `api.js`: Centralized API service with mock implementation

- **Utils**: Utility functions
  - `helpers.js`: Common utility functions

### Performance Optimizations

- **Code Splitting**: Using React.lazy() and Suspense for route-based code splitting
- **Memoization**: Using React.memo() and useMemo() for expensive computations
- **Debouncing**: Implementing debounce for search inputs and other frequent events

### Error Handling

- **Error Boundaries**: Catching and displaying errors gracefully
- **Notifications**: Providing feedback for successful and failed operations
- **Form Validation**: Validating user input before submission

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm 7.x or higher

### Installation

1. Clone the repository
```bash
git clone https://github.com/your-username/cylinder-management-system.git
cd cylinder-management-system
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to http://localhost:5173

### Available Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build the application for production
- `npm run preview`: Preview the production build locally

## Environment Variables

The application uses environment variables for configuration. Create a `.env` file in the root directory with the following variables:

```
# API URL
VITE_API_URL=http://localhost:5000/api

# App settings
VITE_APP_NAME=Cylinder Management System
VITE_COMPANY_NAME=Alpha Gas Solution

# Feature flags
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=false

# Other settings
VITE_MAX_BATCH_SIZE=50
VITE_DEFAULT_PAGINATION_LIMIT=10
```

## User Roles

The application supports the following user roles:

- **Store**: Manages cylinder inventory and generates QR codes
- **Production**: Scans cylinders in production
- **QC**: Performs quality control checks
- **Logistics**: Manages cylinder shipping and receiving
- **Admin**: Has access to all features and administrative functions

## Demo Credentials

For testing purposes, you can use the following credentials:

- Store User: `store_user` / `password`
- Production User: `production_user` / `password`
- QC User: `qc_user` / `password`
- Logistics User: `logistics_user` / `password`
- Admin User: `admin_user` / `password`

## License

This project is licensed under the MIT License - see the LICENSE file for details.
