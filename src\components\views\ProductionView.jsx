import { useState } from 'react';
import { useCylinder } from '../../contexts/cylinder';
import { useAuth } from '../../contexts/auth';
import { useUI } from '../../contexts/ui';
import { QRCodeSVG } from 'qrcode.react';

function ProductionView() {
  const { cylinders, scanCylinder, findCylinderByQrOrId, CYLINDER_STATUS, LOCATIONS } = useCylinder();
  const { user } = useAuth();
  const { addNotification } = useUI();
  const [scanInput, setScanInput] = useState('');
  const [scanResult, setScanResult] = useState(null);
  const [error, setError] = useState('');
  const [showScanner, setShowScanner] = useState(false);

  // Filter cylinders in production
  const productionCylinders = cylinders.filter(c => c.location === LOCATIONS.PRODUCTION);

  const handleScan = (e) => {
    e.preventDefault();
    setError('');
    setScanResult(null);

    try {
      // Find cylinder by ID or QR data
      const cylinder = findCylinderByQrOrId(scanInput);

      if (!cylinder) {
        setError('Cylinder not found');
        return;
      }

      // Check if cylinder can be moved to production
      if (cylinder.location !== LOCATIONS.STORE || cylinder.status !== CYLINDER_STATUS.NEW) {
        setError('Cylinder cannot be moved to production');
        return;
      }

      // Update cylinder status and location
      const updatedCylinder = scanCylinder(
        cylinder.id,
        CYLINDER_STATUS.IN_PRODUCTION,
        LOCATIONS.PRODUCTION,
        'Moved to Production',
        user.name
      );

      setScanResult(updatedCylinder);
      setScanInput('');
    } catch (err) {
      setError(err.message);
    }
  };

  const handleScannerToggle = () => {
    setShowScanner(!showScanner);
  };

  const handleCompleteProduction = (cylinderId) => {
    try {
      const updatedCylinder = scanCylinder(
        cylinderId,
        CYLINDER_STATUS.IN_QC,
        LOCATIONS.QC,
        'Production Complete, Moved to QC',
        user.name
      );

      setScanResult(updatedCylinder);
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Production Management</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Scan Cylinder</h2>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {scanResult && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              Cylinder {scanResult.id} successfully moved to production
            </div>
          )}

          <div className="mb-4">
            <button
              onClick={handleScannerToggle}
              className="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mb-4"
            >
              {showScanner ? 'Hide QR Scanner' : 'Show QR Scanner'}
            </button>

            {showScanner && (
              <div className="mb-4 p-4 border rounded bg-gray-50">
                <p className="mb-2 text-sm text-gray-600">Scan a QR code with your device's camera</p>
                <div className="flex justify-center">
                  <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
                    <p className="text-center text-gray-500">Camera QR Scanner would appear here</p>
                    <p className="text-center text-gray-500 text-sm mt-2">(In a real app, this would use the device camera)</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <form onSubmit={handleScan}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="scanInput">
                Cylinder ID or QR Code
              </label>
              <input
                id="scanInput"
                type="text"
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value={scanInput}
                onChange={(e) => setScanInput(e.target.value)}
                placeholder="Scan or enter cylinder ID or QR data"
                required
                autoFocus
              />
            </div>

            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Scan
            </button>
          </form>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Production Queue</h2>
          {productionCylinders.length === 0 ? (
            <p className="text-gray-600">No cylinders in production.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Gas Type
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Size
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {productionCylinders.map((cylinder) => (
                    <tr key={cylinder.id}>
                      <td className="py-2 px-4 border-b border-gray-200">
                        {cylinder.id}
                        <div className="text-xs text-gray-500 mt-1">
                          QR: {cylinder.qrData}
                        </div>
                      </td>
                      <td className="py-2 px-4 border-b border-gray-200">
                        {cylinder.gasType}
                      </td>
                      <td className="py-2 px-4 border-b border-gray-200">
                        {cylinder.size}
                      </td>
                      <td className="py-2 px-4 border-b border-gray-200">
                        <button
                          onClick={() => handleCompleteProduction(cylinder.id)}
                          className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm"
                        >
                          Complete
                        </button>
                        <div className="mt-2">
                          <QRCodeSVG value={cylinder.qrData} size={40} />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ProductionView;
