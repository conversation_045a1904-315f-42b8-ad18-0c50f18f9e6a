/**
 * Application configuration
 * Uses environment variables with fallbacks
 */

// Debug: Check if environment variables are loaded
if (import.meta.env.DEV) {
  console.log('Environment check:', {
    NODE_ENV: import.meta.env.MODE,
    SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
    SUPABASE_KEY_EXISTS: !!import.meta.env.VITE_SUPABASE_ANON_KEY
  });
}

const config = {
  // API settings
  api: {
    baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000', 10),
  },
  
  // Supabase settings
  supabase: {
    url: import.meta.env.VITE_SUPABASE_URL || '',
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
  },
  
  // App settings
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Cylinder Management System',
    companyName: import.meta.env.VITE_COMPANY_NAME || 'Alpha Gas Solution',
    environment: import.meta.env.MODE || 'development',
    isDevelopment: import.meta.env.MODE === 'development',
    isProduction: import.meta.env.MODE === 'production',
  },
  
  // Feature flags
  features: {
    enableNotifications: import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true',
    enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  },
  
  // Other settings
  settings: {
    maxBatchSize: parseInt(import.meta.env.VITE_MAX_BATCH_SIZE || '50', 10),
    defaultPaginationLimit: parseInt(import.meta.env.VITE_DEFAULT_PAGINATION_LIMIT || '10', 10),
  }
};

export default config;
