// API service with Supabase integration
import supabase from '../lib/supabase.js'
import { logger } from '../utils/logger.js'

/**
 * API service for cylinder operations using Supabase
 */
export const cylinderApi = {
  /**
   * Get all cylinders
   * @returns {Promise<Array>} Promise that resolves with an array of cylinders
   */
  getAllCylinders: async () => {
    try {
      const { data, error } = await supabase
        .from('cylinders')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      logger.error('Error fetching cylinders:', error)
      throw error
    }
  },

  /**
   * Get a cylinder by ID
   * @param {string} id - Cylinder ID
   * @returns {Promise<Object>} Promise that resolves with the cylinder object
   */
  getCylinderById: async (id) => {
    try {
      const { data, error } = await supabase
        .from('cylinders')
        .select('*')
        .or(`id.eq.${id},qr_data.eq.${id}`)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error(`Cylinder with ID ${id} not found`)
        }
        throw error
      }

      return data
    } catch (error) {
      logger.error('Error fetching cylinder:', error)
      throw error
    }
  },

  /**
   * Add a new cylinder
   * @param {Object} cylinderData - Cylinder data
   * @returns {Promise<Object>} Promise that resolves with the new cylinder object
   */
  addCylinder: async (cylinderData) => {
    try {
      // Ensure specifications and history are valid JSON or objects/arrays
      const specs = typeof cylinderData.specifications === 'string' 
        ? JSON.parse(cylinderData.specifications) 
        : cylinderData.specifications || {};
      const hist = Array.isArray(cylinderData.history) 
        ? cylinderData.history 
        : (typeof cylinderData.history === 'string' ? JSON.parse(cylinderData.history) : []);

      const { data, error } = await supabase
        .from('cylinders')
        .insert([{
          id: cylinderData.id,
          type: cylinderData.type,
          status: cylinderData.status,
          location: cylinderData.location,
          qr_data: cylinderData.qrData, // Ensure this matches the expected field name from the form
          specifications: specs,
          history: hist
        }])
        .select(); // Request the inserted data be returned

      if (error) {
        logger.error('Supabase insert error object:', JSON.stringify(error, null, 2));
        if (error.code === '23505') { // Unique constraint violation
          let specificMessage = `Cylinder with ID '${cylinderData.id}' or QR Data '${cylinderData.qrData}' already exists.`;
          if (error.details) {
            if (error.details.includes('(id)')) {
              specificMessage = `Cylinder with Serial Number (ID) '${cylinderData.id}' already exists.`;
            } else if (error.details.includes('(qr_data)')) {
              specificMessage = `Cylinder with QR Data '${cylinderData.qrData}' already exists. Please try a different Serial Number as QR Data is derived from it.`;
            }
          }
          throw new Error(`${specificMessage} Details: ${error.message}`);
        }
        // Check for RLS violation or other specific Postgres errors
        if (error.message.includes('new row violates row-level security policy')) {
          throw new Error('Failed to add cylinder due to security policy. Please check your permissions.');
        }
        throw new Error(`Failed to add cylinder: ${error.message} (Code: ${error.code})`);
      }

      if (!data || data.length === 0) {
        // This case might happen if RLS prevents reading the inserted row,
        // even if the insert itself was successful but didn't return data.
        logger.warn('Cylinder insert operation returned no data. This might be due to RLS. Assuming insert was successful based on no error.');
        // We can construct a presumed object or fetch it separately if critical
        // For now, let's return the input data as a fallback, indicating it might be a partial success
        return { ...cylinderData, specifications: specs, history: hist, _warning: "Data not returned from DB" };
      }

      return data[0]; // .select() without .single() returns an array
    } catch (error) {
      logger.error('Error in addCylinder internal try-catch:', error);
      if (error instanceof SyntaxError) {
        throw new Error(`Invalid JSON format in specifications or history: ${error.message}`);
      }
      throw error; // Re-throw the original or new error
    }
  },

  /**
   * Add multiple cylinders in batch
   * @param {Array} cylindersData - Array of cylinder data
   * @returns {Promise<Array>} Promise that resolves with the array of new cylinders
   */
  addCylindersBatch: async (cylindersData) => {
    try {
      const cylindersForDb = cylindersData.map(cylinder => ({
        id: cylinder.id,
        type: cylinder.type,
        status: cylinder.status,
        location: cylinder.location,
        qr_data: cylinder.qrData,
        specifications: cylinder.specifications,
        history: cylinder.history || []
      }))

      const { data, error } = await supabase
        .from('cylinders')
        .insert(cylindersForDb)
        .select()

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          throw new Error(`Some cylinders already exist`)
        }
        throw error
      }

      return data
    } catch (error) {
      logger.error('Error adding cylinders batch:', error)
      throw error
    }
  },

  /**
   * Update a cylinder
   * @param {string} id - Cylinder ID
   * @param {Object} updates - Object containing the fields to update
   * @returns {Promise<Object>} Promise that resolves with the updated cylinder object
   */
  updateCylinder: async (id, updates) => {
    try {
      const updateData = {
        ...(updates.type && { type: updates.type }),
        ...(updates.status && { status: updates.status }),
        ...(updates.location && { location: updates.location }),
        ...(updates.qrData && { qr_data: updates.qrData }),
        ...(updates.specifications && { specifications: updates.specifications }),
        ...(updates.history && { history: updates.history }),
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('cylinders')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error(`Cylinder with ID ${id} not found`)
        }
        throw error
      }

      return data
    } catch (error) {
      logger.error('Error updating cylinder:', error)
      throw error
    }
  },

  /**
   * Scan a cylinder and update its status
   * @param {string} id - Cylinder ID or QR data
   * @param {string} newStatus - New status
   * @param {string} newLocation - New location
   * @param {string} userName - User who performed the scan
   * @returns {Promise<Object>} Promise that resolves with the updated cylinder object
   */
  scanCylinder: async (id, newStatus, newLocation, userName) => {
    try {
      // First get the current cylinder
      const { data: currentCylinder, error: fetchError } = await supabase
        .from('cylinders')
        .select('*')
        .or(`id.eq.${id},qr_data.eq.${id}`)
        .single()

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          throw new Error(`Cylinder with ID ${id} not found`)
        }
        throw fetchError
      }

      const timestamp = new Date().toISOString()
      
      // Create history entry
      const historyEntry = {
        timestamp,
        action: `Scanned: ${currentCylinder.status} -> ${newStatus}`,
        user: userName,
        status: newStatus,
        location: newLocation
      }

      // Update the cylinder with new status and history
      const updatedHistory = [...(currentCylinder.history || []), historyEntry]
      
      const { data, error } = await supabase
        .from('cylinders')
        .update({
          status: newStatus,
          location: newLocation,
          history: updatedHistory,
          updated_at: timestamp
        })
        .eq('id', currentCylinder.id)
        .select()
        .single()

      if (error) throw error

      return data
    } catch (error) {
      logger.error('Error scanning cylinder:', error)
      throw error
    }
  }
}

/**
 * API service for authentication using Supabase Auth
 */
export const authApi = {
  /**
   * Login a user
   * @param {string} email - User email
   * @param {string} password - Password
   * @returns {Promise<Object>} Promise that resolves with the user object
   */
  login: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      // Get user profile data
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', data.user.id)
        .single()

      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError
      }

      return {
        id: data.user.id,
        email: data.user.email,
        name: profile?.name || data.user.email,
        role: profile?.role || 'Store',
        ...profile
      }
    } catch (error) {
      logger.error('Error logging in:', error)
      throw new Error(error.message || 'Invalid email or password')
    }
  },

  /**
   * Register a new user
   * @param {string} email - User email
   * @param {string} password - Password
   * @param {Object} profileData - Additional profile data (e.g., { name, role })
   * @returns {Promise<Object>} Promise that resolves with the user object from Supabase auth
   */
  register: async (email, password, profileData = {}) => {
    try {
      console.log('[authApi.register] Attempting to sign up:', { email, name: profileData.name, role: profileData.role });
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: profileData, // Pass name, role, etc. here for the trigger
        },
      });

      console.log('[authApi.register] Supabase signUp response:', { data, error });

      if (error) {
        logger.error('[authApi.register] Supabase signUp error:', error);
        throw error;
      }

      // The user_profiles row will be created by the database trigger.
      // The onAuthStateChange listener in AuthContext will handle session updates.
      // data will contain { user, session }. If email confirmation is required, session will be null.
      if (data.user && !data.session) {
        console.log('[authApi.register] User created, email confirmation likely pending.');
      } else if (data.user && data.session) {
        console.log('[authApi.register] User created and session established (email confirmation might be off or already completed).');
      }

      return data; 
    } catch (error) {
      logger.error('[authApi.register] Error registering user:', error.message);
      throw new Error(error.message || 'Failed to register user.');
    }
  },

  /**
   * Get the current user
   * @returns {Promise<Object} Promise that resolves with the user object
   */
  getCurrentUser: async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error) throw error
      if (!user) throw new Error('No user is currently logged in')

      // Get user profile data
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError
      }

      return {
        id: user.id,
        email: user.email,
        name: profile?.name || user.email,
        role: profile?.role || 'Store',
        ...profile
      }
    } catch (error) {
      logger.error('Error getting current user:', error)
      throw error
    }
  },

  /**
   * Logout the current user
   * @returns {Promise<void} Promise that resolves when the user is logged out
   */
  logout: async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (error) {
      logger.error('Error logging out:', error)
      throw error
    }
  },

  /**
   * Listen to auth state changes
   * @param {Function} callback - Callback function to handle auth state changes
   * @returns {Function} Unsubscribe function
   */
  onAuthStateChange: (callback) => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(callback)
    return () => subscription.unsubscribe()
  }
}
