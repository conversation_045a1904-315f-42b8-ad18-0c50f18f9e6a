{"name": "vitest", "type": "module", "version": "0.30.1", "description": "A blazing fast unit test framework powered by Vite", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vitest-dev/vitest#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/vitest"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "keywords": ["vite", "vite-node", "vitest", "test", "jest"], "sideEffects": false, "exports": {".": {"require": {"types": "./index.d.cts", "default": "./index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./*": "./*", "./globals": {"types": "./globals.d.ts"}, "./importMeta": {"types": "./importMeta.d.ts"}, "./node": {"types": "./dist/node.d.ts", "import": "./dist/node.js"}, "./browser": {"types": "./dist/browser.d.ts", "import": "./dist/browser.js"}, "./runners": {"types": "./dist/runners.d.ts", "import": "./dist/runners.js"}, "./suite": {"types": "./dist/suite.d.ts", "import": "./dist/suite.js"}, "./environments": {"types": "./dist/environments.d.ts", "import": "./dist/environments.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}, "./config": {"types": "./config.d.ts", "require": "./dist/config.cjs", "import": "./dist/config.js"}, "./coverage": {"types": "./coverage.d.ts", "import": "./dist/coverage.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"vitest": "./vitest.mjs"}, "files": ["dist", "bin", "*.d.ts", "*.d.cts", "*.mjs", "*.cjs"], "engines": {"node": ">=v14.18.0"}, "peerDependencies": {"@edge-runtime/vm": "*", "@vitest/browser": "*", "@vitest/ui": "*", "happy-dom": "*", "jsdom": "*", "playwright": "*", "safaridriver": "*", "webdriverio": "*"}, "peerDependenciesMeta": {"@vitest/ui": {"optional": true}, "@vitest/browser": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}, "webdriverio": {"optional": true}, "safaridriver": {"optional": true}, "playwright": {"optional": true}, "@edge-runtime/vm": {"optional": true}}, "dependencies": {"@types/chai": "^4.3.4", "@types/chai-subset": "^1.3.3", "@types/node": "*", "acorn": "^8.8.2", "acorn-walk": "^8.2.0", "cac": "^6.7.14", "chai": "^4.3.7", "concordance": "^5.0.4", "debug": "^4.3.4", "local-pkg": "^0.4.3", "magic-string": "^0.30.0", "pathe": "^1.1.0", "picocolors": "^1.0.0", "source-map": "^0.6.1", "std-env": "^3.3.2", "strip-literal": "^1.0.1", "tinybench": "^2.4.0", "tinypool": "^0.4.0", "vite": "^3.0.0 || ^4.0.0", "why-is-node-running": "^2.2.2", "@vitest/snapshot": "0.30.1", "@vitest/expect": "0.30.1", "@vitest/runner": "0.30.1", "@vitest/spy": "0.30.1", "@vitest/utils": "0.30.1", "vite-node": "0.30.1"}, "devDependencies": {"@ampproject/remapping": "^2.2.0", "@antfu/install-pkg": "^0.1.1", "@edge-runtime/vm": "2.1.2", "@sinonjs/fake-timers": "^10.0.2", "@types/diff": "^5.0.3", "@types/istanbul-lib-coverage": "^2.0.4", "@types/istanbul-reports": "^3.0.1", "@types/jsdom": "^21.1.1", "@types/micromatch": "^4.0.2", "@types/prompts": "^2.4.4", "@types/sinonjs__fake-timers": "^8.1.2", "birpc": "0.2.3", "chai-subset": "^1.6.0", "cli-truncate": "^3.1.0", "event-target-polyfill": "^0.0.3", "execa": "^7.1.1", "expect-type": "^0.15.0", "fast-glob": "^3.2.12", "find-up": "^6.3.0", "flatted": "^3.2.7", "get-tsconfig": "^4.5.0", "happy-dom": "^8.9.0", "jsdom": "^21.1.1", "log-update": "^5.0.1", "micromatch": "^4.0.5", "mlly": "^1.2.0", "p-limit": "^4.0.0", "pkg-types": "^1.0.2", "playwright": "^1.32.2", "pretty-format": "^27.5.1", "prompts": "^2.4.2", "safaridriver": "^0.0.4", "strip-ansi": "^7.0.1", "webdriverio": "^8.6.9", "ws": "^8.13.0"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "NODE_OPTIONS=\"--max-old-space-size=8192\" rollup -c --watch -m inline"}}