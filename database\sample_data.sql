-- Migration script to add sample data to the cylinders table
-- Run this after setting up your Supabase database

-- Insert sample cylinders
INSERT INTO cylinders (id, type, status, location, qr_data, specifications, history) VALUES
(
  'CYL001',
  'Oxygen',
  'Available',
  'Store',
  'QR-CYL001',
  '{"capacity": "10L", "pressure": "150bar", "material": "Steel", "weight": "15kg"}',
  '[{"timestamp": "2024-01-15T10:00:00Z", "action": "Created", "user": "System", "status": "Available", "location": "Store"}]'
),
(
  'CYL002',
  'Nitrogen',
  'In Use',
  'Production',
  'QR-CYL002',
  '{"capacity": "20L", "pressure": "200bar", "material": "Aluminum", "weight": "12kg"}',
  '[
    {"timestamp": "2024-01-15T10:00:00Z", "action": "Created", "user": "System", "status": "Available", "location": "Store"},
    {"timestamp": "2024-01-16T08:30:00Z", "action": "Scanned: Available -> In Use", "user": "Production User", "status": "In Use", "location": "Production"}
  ]'
),
(
  'CYL003',
  'Argon',
  'Maintenance',
  'QC',
  'QR-CYL003',
  '{"capacity": "15L", "pressure": "180bar", "material": "Steel", "weight": "18kg"}',
  '[
    {"timestamp": "2024-01-15T10:00:00Z", "action": "Created", "user": "System", "status": "Available", "location": "Store"},
    {"timestamp": "2024-01-17T14:15:00Z", "action": "Scanned: Available -> Maintenance", "user": "QC User", "status": "Maintenance", "location": "QC"}
  ]'
),
(
  'CYL004',
  'CO2',
  'Available',
  'Store',
  'QR-CYL004',
  '{"capacity": "25L", "pressure": "220bar", "material": "Steel", "weight": "22kg"}',
  '[{"timestamp": "2024-01-15T10:00:00Z", "action": "Created", "user": "System", "status": "Available", "location": "Store"}]'
),
(
  'CYL005',
  'Helium',
  'In Transit',
  'Logistics',
  'QR-CYL005',
  '{"capacity": "12L", "pressure": "160bar", "material": "Aluminum", "weight": "10kg"}',
  '[
    {"timestamp": "2024-01-15T10:00:00Z", "action": "Created", "user": "System", "status": "Available", "location": "Store"},
    {"timestamp": "2024-01-18T09:00:00Z", "action": "Scanned: Available -> In Transit", "user": "Logistics User", "status": "In Transit", "location": "Logistics"}
  ]'
);

-- Verify the data was inserted
SELECT COUNT(*) as total_cylinders FROM cylinders;
SELECT status, COUNT(*) as count FROM cylinders GROUP BY status;
SELECT location, COUNT(*) as count FROM cylinders GROUP BY location;
