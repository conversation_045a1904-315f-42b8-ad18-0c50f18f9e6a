{"name": "cylinder-management-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext .js,.jsx", "format": "prettier --write 'src/**/*.{js,jsx,css}'"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "lucide-react": "^0.487.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "jsdom": "^21.1.1", "postcss": "^8.4.21", "prettier": "^2.8.7", "tailwindcss": "^3.3.1", "vite": "^4.2.1", "vitest": "^0.30.1"}}