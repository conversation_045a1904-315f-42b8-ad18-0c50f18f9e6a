import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import { AuthProvider, useAuth } from './contexts/auth/AuthContext';
import { CylinderProvider } from './contexts/cylinder/CylinderContext';
import { UIProvider } from './contexts/ui/UIContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import Notifications from './components/common/Notifications';
import { GlobalLoadingSpinner } from './components/common/LoadingSpinner';

// Lazy load components for code splitting
const LoginScreen = lazy(() => import('./components/auth/LoginScreen'));
const RegistrationScreen = lazy(() => import('./components/auth/RegistrationScreen'));
const DashboardLayout = lazy(() => import('./components/layout/DashboardLayout'));
const Overview = lazy(() => import('./components/views/Overview'));
const StoreView = lazy(() => import('./components/views/StoreView'));
const ProductionView = lazy(() => import('./components/views/ProductionView'));
const QCView = lazy(() => import('./components/views/QCView'));
const LogisticsView = lazy(() => import('./components/views/LogisticsView'));
const AdminView = lazy(() => import('./components/views/AdminView'));
const SettingsView = lazy(() => import('./components/views/SettingsView'));

// Loading component for suspense fallback
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-screen bg-secondary-50">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-secondary-600 font-medium">Loading...</p>
    </div>
  </div>
);

// Protected route component that redirects to login if not authenticated
function ProtectedRoute({ children, allowedRoles = [] }) {
  const { user, isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingFallback />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has required role (if roles are specified)
  if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
}

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <AuthProvider>
          <CylinderProvider>
            <UIProvider>
              <Notifications />
              <GlobalLoadingSpinner />
              <Suspense fallback={<LoadingFallback />}>
                <Routes>
                  <Route path="/login" element={<LoginScreen />} />
                  <Route path="/register" element={<RegistrationScreen />} /> {/* Added this line */}

                  {/* Protected Routes */}
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <Overview />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/store" element={
                    <ProtectedRoute allowedRoles={['Store', 'Admin']}>
                      <DashboardLayout>
                        <StoreView />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/production" element={
                    <ProtectedRoute allowedRoles={['Production', 'Admin']}>
                      <DashboardLayout>
                        <ProductionView />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/qc" element={
                    <ProtectedRoute allowedRoles={['QC', 'Admin']}>
                      <DashboardLayout>
                        <QCView />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/logistics" element={
                    <ProtectedRoute allowedRoles={['Logistics', 'Admin']}>
                      <DashboardLayout>
                        <LogisticsView />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/admin" element={
                    <ProtectedRoute allowedRoles={['Admin']}>
                      <DashboardLayout>
                        <AdminView />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <SettingsView />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } />
                  <Route path="/" element={<Navigate to="/login" replace />} />
                  <Route path="*" element={<Navigate to="/login" replace />} />
                </Routes>
              </Suspense>
            </UIProvider>
          </CylinderProvider>
        </AuthProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;