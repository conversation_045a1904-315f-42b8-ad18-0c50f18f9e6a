import { createContext, useContext, useState, useEffect } from 'react';
import { cylinderApi } from '../../services/api.js';

// Constants
export const CYLINDER_STATUS = {
  AVAILABLE: 'Available',
  IN_USE: 'In Use',
  MAINTENANCE: 'Maintenance',
  IN_TRANSIT: 'In Transit',
  OUT_OF_SERVICE: 'Out of Service'
};

export const LOCATIONS = {
  STORE: 'Store',
  PRODUCTION: 'Production',
  QC: 'QC',
  LOGISTICS: 'Logistics',
  CUSTOMER: 'Customer'
};

const CylinderContext = createContext(null);

export function CylinderProvider({ children }) {
  const [cylinders, setCylinders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load cylinders on mount
  useEffect(() => {
    loadCylinders();
  }, []);

  const loadCylinders = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await cylinderApi.getAllCylinders();
      setCylinders(data);
    } catch (err) {
      console.error('Error loading cylinders:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const addCylinder = async (cylinderData) => {
    try {
      setLoading(true);
      setError(null);
      const newCylinder = await cylinderApi.addCylinder(cylinderData);
      setCylinders(prev => [...prev, newCylinder]);
      return newCylinder;
    } catch (err) {
      console.error('Error adding cylinder:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const addCylindersBatch = async (cylindersData) => {
    try {
      setLoading(true);
      setError(null);
      const newCylinders = await cylinderApi.addCylindersBatch(cylindersData);
      setCylinders(prev => [...prev, ...newCylinders]);
      return newCylinders;
    } catch (err) {
      console.error('Error adding cylinders batch:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateCylinder = async (id, updates) => {
    try {
      setLoading(true);
      setError(null);
      const updatedCylinder = await cylinderApi.updateCylinder(id, updates);
      setCylinders(prev => 
        prev.map(cylinder => 
          cylinder.id === id ? updatedCylinder : cylinder
        )
      );
      return updatedCylinder;
    } catch (err) {
      console.error('Error updating cylinder:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const scanCylinder = async (id, newStatus, newLocation, userName) => {
    try {
      setLoading(true);
      setError(null);
      const updatedCylinder = await cylinderApi.scanCylinder(id, newStatus, newLocation, userName);
      setCylinders(prev => 
        prev.map(cylinder => 
          cylinder.id === updatedCylinder.id ? updatedCylinder : cylinder
        )
      );
      return updatedCylinder;
    } catch (err) {
      console.error('Error scanning cylinder:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getCylinderById = async (id) => {
    try {
      setError(null);
      return await cylinderApi.getCylinderById(id);
    } catch (err) {
      console.error('Error getting cylinder by ID:', err);
      setError(err.message);
      throw err;
    }
  };

  // Helper functions for filtering
  const getCylindersByStatus = (status) => {
    return cylinders.filter(cylinder => cylinder.status === status);
  };

  const getCylindersByLocation = (location) => {
    return cylinders.filter(cylinder => cylinder.location === location);
  };

  const getCylindersByType = (type) => {
    return cylinders.filter(cylinder => cylinder.type === type);
  };

  const getStatusCounts = () => {
    return Object.values(CYLINDER_STATUS).reduce((counts, status) => {
      counts[status] = cylinders.filter(c => c.status === status).length;
      return counts;
    }, {});
  };

  const getLocationCounts = () => {
    return Object.values(LOCATIONS).reduce((counts, location) => {
      counts[location] = cylinders.filter(c => c.location === location).length;
      return counts;
    }, {});
  };

  return (
    <CylinderContext.Provider value={{
      cylinders,
      loading,
      error,
      loadCylinders,
      addCylinder,
      addCylindersBatch,
      updateCylinder,
      scanCylinder,
      getCylinderById,
      getCylindersByStatus,
      getCylindersByLocation,
      getCylindersByType,
      getStatusCounts,
      getLocationCounts,
      CYLINDER_STATUS,
      LOCATIONS
    }}>
      {children}
    </CylinderContext.Provider>
  );
}

export function useCylinder() {
  const context = useContext(CylinderContext);
  if (!context) {
    throw new Error('useCylinder must be used within a CylinderProvider');
  }
  return context;
}
