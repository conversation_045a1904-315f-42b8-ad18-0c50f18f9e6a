import { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { authApi } from '../../services/api.js';
import supabase from '../../lib/supabase.js'; // Direct import for onAuthStateChange
import { logger } from '../../utils/logger.js';

// Sample user roles
export const ROLES = {
  STORE: 'Store',
  PRODUCTION: 'Production',
  QC: 'QC',
  LOGISTICS: 'Logistics',
  ADMIN: 'Admin'
};

const AuthContext = createContext(null);

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true); // Initialize loading to true
  const [error, setError] = useState(null);
  const [authEvent, setAuthEvent] = useState(null); // Declare setAuthEvent

  const login = useCallback(async (email, password) => {
    setLoading(true);
    setError(null);
    try {
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (signInError) throw signInError;

      // onAuthStateChange will handle setting the user and session
      // No need to manually fetch profile here as SIGNED_IN event will trigger it
      logger.log('Login successful in AuthContext.login, waiting for SIGNED_IN event');
      // setLoading(false); // setLoading will be handled by onAuthStateChange
      return data; // Contains user and session if successful
    } catch (e) {
      logger.error('Error in AuthContext login:', e);
      setError(e.message);
      setLoading(false); // Ensure loading is false on error here
      throw e;
    }
  }, []); // Empty dependency array as supabase and setLoading/setError are stable

  const register = useCallback(async (email, password, profileData) => {
    setLoading(true);
    setError(null);
    try {
      // The authApi.register function already handles the signUp call
      const data = await authApi.register(email, password, profileData);
      // onAuthStateChange will handle setting the user and session if auto-login or confirmation
      logger.log('Registration successful in AuthContext.register, waiting for auth event');
      // setLoading(false); // setLoading will be handled by onAuthStateChange
      return data;
    } catch (e) {
      logger.error('Error in AuthContext register:', e);
      setError(e.message);
      setLoading(false); // Ensure loading is false on error here
      throw e;
    }
  }, []); // authApi is stable

  const logout = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const { error: signOutError } = await supabase.auth.signOut();
      if (signOutError) throw signOutError;
      // onAuthStateChange will handle clearing user and session
      logger.log('Logout successful in AuthContext.logout, waiting for SIGNED_OUT event');
      // User, session, isAuthenticated will be reset by onAuthStateChange 'SIGNED_OUT' event
      // setLoading(false); // setLoading will be handled by onAuthStateChange
    } catch (e) {
      logger.error('Error in AuthContext logout:', e);
      setError(e.message);
      setLoading(false); // Ensure loading is false on error here
      throw e;
    }
  }, []);

  useEffect(() => {
    setLoading(true);
    logger.log('AuthContext: useEffect mounted, setting up onAuthStateChange listener.');

    const handleAuthEvent = async (event, currentSession) => {
      logger.log(`Auth event: ${event}`, currentSession);
      setAuthEvent({ event, session: currentSession }); // For debugging

      let profileToSet = user;
      let newIsAuthenticated = isAuthenticated;
      let authError = error; 
      let newSessionState = currentSession;

      switch (event) {
        case 'INITIAL_SESSION':
        case 'SIGNED_IN':
        case 'USER_UPDATED':
          newSessionState = currentSession;
          if (currentSession && currentSession.user) {
            newIsAuthenticated = true;
            try {
              logger.log(`${event}: Fetching profile for user ID: ${currentSession.user.id}`);
              const { data: profileData, error: profileFetchError } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('id', currentSession.user.id)
                .single();

              if (profileFetchError && profileFetchError.code !== 'PGRST116') { // PGRST116: row not found
                logger.error(`${event}: Error fetching user profile:`, profileFetchError);
                profileToSet = currentSession.user; // Fallback to auth user
                authError = profileFetchError.message;
              } else if (profileData) {
                logger.log(`${event}: Profile fetched successfully:`, profileData);
                profileToSet = { ...currentSession.user, ...profileData };
                authError = null; // Clear error on successful profile fetch
              } else {
                logger.log(`${event}: No profile found, using auth user data.`);
                profileToSet = currentSession.user;
                authError = null; // Clear error if no profile is not an error condition
              }
            } catch (e) {
              logger.error(`${event}: Exception during profile fetch:`, e);
              profileToSet = currentSession.user; // Fallback
              authError = e.message;
            }
          } else {
            // No user in session (e.g. INITIAL_SESSION with no active session, or problematic SIGNED_IN)
            profileToSet = null;
            newIsAuthenticated = false;
            authError = null; 
            if (event === 'SIGNED_IN' && (!currentSession || !currentSession.user)) {
               logger.warn('SIGNED_IN event, but no user in session or session is null. This is unexpected.');
            }
          }
          break;

        case 'SIGNED_OUT':
          profileToSet = null;
          newSessionState = null;
          newIsAuthenticated = false;
          authError = null;
          break;

        case 'PASSWORD_RECOVERY':
          logger.log('PASSWORD_RECOVERY: Event received.');
          // UI might show a message. Loading state might need adjustment.
          break;

        case 'TOKEN_REFRESHED':
          logger.log('TOKEN_REFRESHED: Session updated.');
          newSessionState = currentSession; // Session is already updated by Supabase
          // User object might not change, profile refetch might not be needed unless USER_UPDATED also fires.
          break;
          
        default:
          logger.log(`Unhandled auth event: ${event}`);
      }

      setUser(profileToSet);
      setSession(newSessionState);
      setIsAuthenticated(newIsAuthenticated);
      setError(authError);
      
      const eventsEndingLoading = ['INITIAL_SESSION', 'SIGNED_IN', 'SIGNED_OUT', 'USER_UPDATED', 'PASSWORD_RECOVERY', 'TOKEN_REFRESHED'];
      if (eventsEndingLoading.includes(event)) {
          logger.log(`${event}: Setting loading to false.`);
          setLoading(false);
      }
    };

    const { data: { subscription } } = supabase.auth.onAuthStateChange(handleAuthEvent);

    return () => {
      logger.log('AuthContext: Unsubscribing from onAuthStateChange.');
      subscription?.unsubscribe();
    };
  }, []); // Empty dependency array ensures this runs once on mount and cleans up on unmount

  const updateUser = (newUserData) => {
    setUser(prevUser => ({ ...prevUser, ...newUserData }));
  };

  const hasRole = (role) => {
    return user?.role === role || user?.role === ROLES.ADMIN;
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      isAuthenticated, 
      loading,
      login, 
      register,
      logout, 
      updateUser,
      hasRole,
      ROLES 
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
