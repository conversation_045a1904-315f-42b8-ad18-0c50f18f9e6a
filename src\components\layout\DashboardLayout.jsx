import { useState, useEffect } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/auth/AuthContext';
import { useUI } from '../../contexts/ui/UIContext';
import {
  Package, Factory, CheckCircle, Truck, Settings,
  LogOut, Menu, X, User, BarChart4
} from 'lucide-react';

function DashboardLayout({ children }) {
  const { user, logout, hasRole, ROLES } = useAuth();
  const { addNotification } = useUI();
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Close mobile menu when location changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Navigation items with role-based access control
  const navItems = [
    {
      path: '/dashboard',
      label: 'Overview',
      icon: <BarChart4 className="h-5 w-5" />,
      roles: [] // Empty array means all roles can access
    },
    {
      path: '/store',
      label: 'Store',
      icon: <Package className="h-5 w-5" />,
      roles: [ROLES.STORE, ROLES.ADMIN],
      badge: 'QR'
    },
    {
      path: '/production',
      label: 'Production',
      icon: <Factory className="h-5 w-5" />,
      roles: [ROLES.PRODUCTION, ROLES.ADMIN]
    },
    {
      path: '/qc',
      label: 'Quality Control',
      icon: <CheckCircle className="h-5 w-5" />,
      roles: [ROLES.QC, ROLES.ADMIN]
    },
    {
      path: '/logistics',
      label: 'Logistics',
      icon: <Truck className="h-5 w-5" />,
      roles: [ROLES.LOGISTICS, ROLES.ADMIN]
    },
    {
      path: '/settings',
      label: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      roles: [ROLES.ADMIN]
    },
    {
      path: '/logout',
      label: 'Logout',
      icon: <LogOut className="h-5 w-5" />,
      roles: [],
      onClick: handleLogout,
      className: 'mt-auto'
    }
  ];

  // Filter navItems based on user roles
  const filteredNavItems = navItems.filter(item =>
    item.roles.length === 0 || item.roles.some(role => hasRole(role))
  );

  return (
    <div className="flex h-screen overflow-hidden bg-secondary-50">
      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`${isSidebarCollapsed ? 'w-20' : 'w-64'} bg-primary-800 text-white fixed h-full z-30 transition-all duration-300 ease-in-out transform ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="py-5 px-4 flex items-center justify-between border-b border-primary-700">
            <div className="flex flex-col overflow-hidden">
              {isSidebarCollapsed ? (
                <div className="mx-auto">
                  <img
                    src="/src/assets/ags-logo.png"
                    alt="Alpha Gas Solution"
                    className="h-10 w-auto"
                  />
                </div>
              ) : (
                <>
                  <div className="flex items-center mb-3">
                    <img
                      src="/src/assets/ags-logo.png"
                      alt="Alpha Gas Solution"
                      className="h-8 w-auto"
                    />
                  </div>
                  <div>
                    <h1 className="text-base font-semibold text-white leading-tight">Cylinder Management</h1>
                    <p className="text-xs font-light text-primary-200">Alpha Gas Solution</p>
                  </div>
                </>
              )}
            </div>
            <button
              className="text-white p-1.5 rounded-md hover:bg-primary-700 md:block hidden flex-shrink-0 transition-colors"
              onClick={() => setSidebarCollapsed(!isSidebarCollapsed)}
              aria-label={isSidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {isSidebarCollapsed ?
                <Menu className="h-5 w-5" /> :
                <X className="h-5 w-5" />
              }
            </button>
          </div>

          {/* User info */}
          <div className={`p-4 border-b border-primary-700 ${isSidebarCollapsed ? 'text-center' : ''}`}>
            <div className="flex items-center space-x-3">
              <div className="bg-primary-600 p-2 rounded-full">
                <User className="h-6 w-6" />
              </div>
              {!isSidebarCollapsed && (
                <div className="overflow-hidden">
                  <p className="font-medium truncate">{user?.name}</p>
                  <p className="text-xs text-primary-200 truncate">{user?.role}</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-4 px-2">
            <ul className="space-y-2">
              {filteredNavItems.map((item, index) => (
                <li key={index} className={item.className}>
                  <Link
                    to={item.path}
                    className={`flex items-center ${isSidebarCollapsed ? 'justify-center' : 'justify-between'} p-2 rounded-lg transition-colors duration-200 ${location.pathname === item.path ? 'bg-primary-700 text-white' : 'text-primary-100 hover:bg-primary-700 hover:text-white'}`}
                    onClick={item.onClick}
                  >
                    <div className="flex items-center">
                      <div className={`${isSidebarCollapsed ? '' : 'mr-3'}`}>
                        {item.icon}
                      </div>
                      {!isSidebarCollapsed && (
                        <span className="truncate">{item.label}</span>
                      )}
                    </div>
                    {!isSidebarCollapsed && item.badge && (
                      <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-primary-800 bg-primary-100 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className={`flex-1 transition-all duration-300 ${isSidebarCollapsed ? 'md:ml-20' : 'md:ml-64'} pt-4 h-screen overflow-y-auto`}>
        {/* Mobile header */}
        <div className="md:hidden bg-primary-800 text-white p-4 flex items-center justify-between sticky top-0 z-10">
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-3">
              <img
                src="/src/assets/ags-logo.png"
                alt="Alpha Gas Solution"
                className="h-8 w-auto"
              />
            </div>
            <div>
              <h1 className="text-base font-semibold text-white leading-tight">Cylinder Management</h1>
              <p className="text-xs font-light text-primary-200">Alpha Gas Solution</p>
            </div>
          </div>
          <button
            className="text-white p-1.5 rounded-md hover:bg-primary-700 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>

        {/* Page content */}
        <div className="p-4 md:p-6 max-w-7xl mx-auto pb-20">
          {children}
        </div>
      </div>
    </div>
  );
}

export default DashboardLayout;
