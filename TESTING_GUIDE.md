# Cylinder Management System - Testing Guide

## 🎉 Integration Complete!

Your Cylinder Management System is now fully integrated with Supabase! Here's how to test everything:

## Current Status
✅ Environment variables configured  
✅ Database schema applied  
✅ Sample data inserted (5 cylinders)  
✅ User profile trigger created  
✅ Row Level Security (RLS) enabled  
✅ Development server running on http://localhost:5174/  

## Test Steps

### 1. Test User Registration & Login

**Create a New Account:**
1. Open http://localhost:5174/ in your browser
2. You should see the login screen
3. Click "Sign Up" or create account option
4. Use a valid email (e.g., <EMAIL>) and password
5. The system will automatically:
   - Create user in Supabase Auth
   - Create user profile with default "Store" role
   - Log you in automatically

**Test Login:**
1. After registration, try logging out
2. Log back in with your credentials
3. Should work seamlessly

### 2. Test Cylinder Data

**View Cylinders:**
- Once logged in, you should see 5 sample cylinders:
  - CYL001 (Oxygen) - Available in Store
  - CYL002 (Nitrogen) - In Use in Production  
  - CYL003 (Argon) - Maintenance in QC
  - CYL004 (CO2) - Available in Store
  - CYL005 (Helium) - In Transit in Logistics

**Test Operations:**
- Try scanning a cylinder (if QR functionality exists)
- Update cylinder status
- Move cylinders between locations
- Add new cylinders

### 3. Test Role-Based Access

**Default Role:**
- New users get "Store" role by default
- Test what features are available

**Admin Features:**
- To test admin features, update your user role:
  1. Go to Supabase Dashboard → Table Editor
  2. Find your user in `user_profiles` table
  3. Change role from "Store" to "Admin"
  4. Refresh your app to see admin features

### 4. Test Data Persistence

**Real Database:**
- All changes now persist in Supabase
- No more localStorage - data is shared across devices
- Refresh browser - data should remain
- Close/reopen browser - should stay logged in (session management)

## Database Access

**Supabase Dashboard:**
- URL: https://supabase.com/dashboard/project/cajtrgylgdnozueilddo
- Use your Supabase account to view:
  - Authentication users
  - Database tables
  - Real-time activity

**Tables Created:**
- `user_profiles` - User information and roles
- `cylinders` - Cylinder inventory data

## Features Now Working

### ✅ Authentication
- Email/password sign up
- Secure login/logout
- Session management
- Automatic user profile creation

### ✅ Database Integration
- Real-time data sync
- CRUD operations on cylinders
- User role management
- Data persistence

### ✅ Security
- Row Level Security (RLS) policies
- User-based data access
- Secure API calls

## Troubleshooting

**If login doesn't work:**
1. Check browser console for errors
2. Verify environment variables in `.env.local`
3. Check Supabase project status in dashboard

**If cylinder data doesn't load:**
1. Check if user is authenticated
2. Verify RLS policies allow data access
3. Check network tab for API calls

**Common Issues:**
- CORS errors: Verify Supabase URL in config
- Auth errors: Check API keys and project settings
- Data not loading: Verify RLS policies

## Next Steps

1. **Test thoroughly** with the steps above
2. **Customize user roles** and permissions as needed
3. **Add more sample data** if required
4. **Deploy to production** when ready

## Support

If you encounter any issues:
1. Check the browser console for errors
2. Review Supabase logs in the dashboard
3. Verify all environment variables are correct

Your app is now production-ready with a real database backend! 🚀
