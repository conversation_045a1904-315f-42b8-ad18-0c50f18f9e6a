import { createContext, useContext, useState } from 'react';

const UIContext = createContext(null);

export function UIProvider({ children }) {
  const [notifications, setNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modalState, setModalState] = useState({
    isOpen: false,
    type: null,
    data: null
  });

  // Add a notification
  const addNotification = (message, type = 'info', duration = 5000) => {
    const id = Date.now();
    const notification = { id, message, type, duration };
    setNotifications(prev => [...prev, notification]);
    
    // Auto-remove notification after duration
    if (duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, duration);
    }
    
    return id;
  };

  // Remove a notification by ID
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // Show loading state
  const showLoading = () => {
    setIsLoading(true);
  };

  // Hide loading state
  const hideLoading = () => {
    setIsLoading(false);
  };

  // Open a modal
  const openModal = (type, data = null) => {
    setModalState({ isOpen: true, type, data });
  };

  // Close the modal
  const closeModal = () => {
    setModalState({ isOpen: false, type: null, data: null });
  };

  return (
    <UIContext.Provider value={{
      notifications,
      isLoading,
      modalState,
      addNotification,
      removeNotification,
      showLoading,
      hideLoading,
      openModal,
      closeModal
    }}>
      {children}
    </UIContext.Provider>
  );
}

// Custom hook for using UI context
export function useUI() {
  const context = useContext(UIContext);
  if (!context) {
    throw new Error('useUI must be used within a UIProvider');
  }
  return context;
}
