import React from 'react';
import { useUI } from '../../contexts/ui';

/**
 * Loading spinner component
 * @param {Object} props - Component props
 * @param {boolean} props.fullScreen - Whether to display full screen
 * @param {string} props.size - Size of the spinner (sm, md, lg)
 * @param {string} props.color - Color of the spinner
 * @returns {JSX.Element} Loading spinner component
 */
function LoadingSpinner({ fullScreen = false, size = 'md', color = 'primary' }) {
  const sizeClasses = {
    sm: 'w-5 h-5 border-2',
    md: 'w-8 h-8 border-3',
    lg: 'w-12 h-12 border-4'
  };
  
  const colorClasses = {
    primary: 'border-primary-600 border-t-transparent',
    secondary: 'border-secondary-600 border-t-transparent',
    white: 'border-white border-t-transparent'
  };
  
  const spinnerClasses = `${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-spin`;
  
  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-secondary-900/50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
          <div className={spinnerClasses}></div>
          <p className="mt-4 text-secondary-600 font-medium">Loading...</p>
        </div>
      </div>
    );
  }
  
  return <div className={spinnerClasses}></div>;
}

/**
 * Global loading spinner that shows when isLoading is true in UI context
 * @returns {JSX.Element|null} Loading spinner or null
 */
export function GlobalLoadingSpinner() {
  const { isLoading } = useUI();
  
  if (!isLoading) {
    return null;
  }
  
  return <LoadingSpinner fullScreen />;
}

export default LoadingSpinner;
