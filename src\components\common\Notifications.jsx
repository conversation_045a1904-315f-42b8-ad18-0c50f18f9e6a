import { useEffect } from 'react';
import { useUI } from '../../contexts/ui/UIContext';
import { X, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';

const NotificationIcons = {
  success: <CheckCircle className="h-5 w-5" />,
  error: <AlertCircle className="h-5 w-5" />,
  warning: <AlertTriangle className="h-5 w-5" />,
  info: <Info className="h-5 w-5" />
};

const NotificationColors = {
  success: 'bg-green-50 text-green-800 border-green-200',
  error: 'bg-red-50 text-red-800 border-red-200',
  warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
  info: 'bg-blue-50 text-blue-800 border-blue-200'
};

const NotificationIconColors = {
  success: 'text-green-500',
  error: 'text-red-500',
  warning: 'text-yellow-500',
  info: 'text-blue-500'
};

function Notification({ notification, onClose }) {
  const { id, message, type = 'info' } = notification;

  useEffect(() => {
    // Auto-remove notification after duration if duration is provided
    if (notification.duration > 0) {
      const timer = setTimeout(() => {
        onClose(id);
      }, notification.duration);

      return () => clearTimeout(timer);
    }
  }, [id, notification.duration, onClose]);

  return (
    <div
      className={`flex items-center p-4 mb-3 rounded-lg border shadow-sm ${NotificationColors[type]}`}
      role="alert"
    >
      <div className={`flex-shrink-0 mr-3 ${NotificationIconColors[type]}`}>
        {NotificationIcons[type]}
      </div>
      <div className="flex-1 mr-2">
        <p className="text-sm font-medium">{message}</p>
      </div>
      <button
        onClick={() => onClose(id)}
        className="flex-shrink-0 ml-auto text-gray-400 hover:text-gray-500 focus:outline-none"
        aria-label="Close notification"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
}

function Notifications() {
  const { notifications, removeNotification } = useUI();

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 w-full max-w-sm">
      {notifications.map(notification => (
        <Notification
          key={notification.id}
          notification={notification}
          onClose={removeNotification}
        />
      ))}
    </div>
  );
}

export default Notifications;
