import { getSafeTimers } from '@vitest/utils';
import { g as getWorkerState } from './vendor-global.6795f91f.js';

const { get } = Reflect;
const safeRandom = Math.random;
function withSafeTimers(fn) {
  const { setTimeout, clearTimeout, nextTick, setImmediate, clearImmediate } = getSafeTimers();
  const currentSetTimeout = globalThis.setTimeout;
  const currentClearTimeout = globalThis.clearTimeout;
  const currentRandom = globalThis.Math.random;
  const currentNextTick = globalThis.process.nextTick;
  const currentSetImmediate = globalThis.setImmediate;
  const currentClearImmediate = globalThis.clearImmediate;
  try {
    globalThis.setTimeout = setTimeout;
    globalThis.clearTimeout = clearTimeout;
    globalThis.Math.random = safeRandom;
    globalThis.process.nextTick = nextTick;
    globalThis.setImmediate = setImmediate;
    globalThis.clearImmediate = clearImmediate;
    const result = fn();
    return result;
  } finally {
    globalThis.setTimeout = currentSetTimeout;
    globalThis.clearTimeout = currentClearTimeout;
    globalThis.Math.random = currentRandom;
    globalThis.setImmediate = currentSetImmediate;
    globalThis.clearImmediate = currentClearImmediate;
    nextTick(() => {
      globalThis.process.nextTick = currentNextTick;
    });
  }
}
const promises = /* @__PURE__ */ new Set();
async function rpcDone() {
  if (!promises.size)
    return;
  const awaitable = Array.from(promises);
  return Promise.all(awaitable);
}
function rpc() {
  const { rpc: rpc2 } = getWorkerState();
  return new Proxy(rpc2, {
    get(target, p, handler) {
      const sendCall = get(target, p, handler);
      const safeSendCall = (...args) => withSafeTimers(async () => {
        const result = sendCall(...args);
        promises.add(result);
        try {
          return await result;
        } finally {
          promises.delete(result);
        }
      });
      safeSendCall.asEvent = sendCall.asEvent;
      return safeSendCall;
    }
  });
}

export { rpcDone as a, rpc as r };
