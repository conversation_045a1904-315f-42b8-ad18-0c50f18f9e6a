@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  @apply bg-secondary-50;
  overflow-x: hidden;
}

/* Improve scrolling on mobile */
.overflow-y-auto {
  -webkit-overflow-scrolling: touch;
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-sm {
    @apply px-3 py-1 text-sm;
  }

  .card {
    @apply bg-white rounded-lg shadow-card hover:shadow-card-hover transition-shadow duration-300;
  }

  .input {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }

  .select {
    @apply input appearance-none bg-no-repeat;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
  }

  .label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }

  .table-container {
    @apply overflow-x-auto rounded-lg border border-secondary-200;
  }

  .table {
    @apply min-w-full divide-y divide-secondary-200;
  }

  .table-header {
    @apply bg-secondary-50;
  }

  .table-header-cell {
    @apply px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-secondary-200;
  }

  .table-row {
    @apply hover:bg-secondary-50;
  }

  .table-cell {
    @apply px-4 py-3 whitespace-nowrap text-sm text-secondary-900;
  }
}
