import { Component } from 'react';
import { X } from 'lucide-react';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // You can log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
    
    // Here you could send the error to your error tracking service
    // e.g., Sentry, LogRocket, etc.
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-secondary-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full overflow-hidden">
            <div className="bg-red-600 text-white px-6 py-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Something went wrong</h2>
              <button 
                onClick={this.handleReset}
                className="text-white hover:text-red-100 focus:outline-none"
                aria-label="Close error message"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <p className="text-secondary-700 mb-2">
                  An error occurred in the application. You can try the following:
                </p>
                <ul className="list-disc pl-5 text-secondary-600 space-y-1">
                  <li>Refresh the page</li>
                  <li>Clear your browser cache</li>
                  <li>Try again later</li>
                </ul>
              </div>
              
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-4 bg-secondary-50 rounded border border-secondary-200 overflow-auto">
                  <p className="font-medium text-red-600 mb-2">Error Details:</p>
                  <pre className="text-xs text-secondary-700 whitespace-pre-wrap">
                    {this.state.error && this.state.error.toString()}
                    {this.state.errorInfo && this.state.errorInfo.componentStack}
                  </pre>
                </div>
              )}
              
              <div className="mt-6 flex justify-center">
                <button
                  onClick={this.handleReset}
                  className="btn-primary"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
