{"hash": "6a731031", "browserHash": "2ccd2bbf", "optimized": {"react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c8f5ddfa", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "da183aed", "needsInterop": true}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ea9bcca3", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5b5d39d1", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "10800b2c", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a556a417", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "ed9e4e76", "needsInterop": false}}, "chunks": {"browser-FHJERFYW": {"file": "browser-FHJERFYW.js"}, "chunk-ZRNALROW": {"file": "chunk-ZRNALROW.js"}, "chunk-LXGCQ6UQ": {"file": "chunk-LXGCQ6UQ.js"}, "browser-ZONL5W77": {"file": "browser-ZONL5W77.js"}, "chunk-QH3POG6S": {"file": "chunk-QH3POG6S.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}