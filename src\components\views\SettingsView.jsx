import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/auth';
import { useUI } from '../../contexts/ui';
import { useCylinder } from '../../contexts/cylinder';
import { Save, X, User, Users, Sliders, Bell, FileText, Database, Paintbrush, Shield, Key, UserPlus } from 'lucide-react';
import config from '../../config';

function SettingsView() {
  const { user, updateUser, ROLES } = useAuth();
  const { addNotification, showLoading, hideLoading } = useUI();
  const { CYLINDER_STATUS, LOCATIONS } = useCylinder();

  // State for all settings sections
  const [activeTab, setActiveTab] = useState('profile');
  
  // Check if user is admin
  const isAdmin = user?.role === ROLES.ADMIN;
  
  // Sample users for the admin to manage
  const [managedUsers, setManagedUsers] = useState([
    { id: 1, username: 'store_user', name: 'Store User', email: '<EMAIL>', role: ROLES.STORE, active: true },
    { id: 2, username: 'production_user', name: 'Production User', email: '<EMAIL>', role: ROLES.PRODUCTION, active: true },
    { id: 3, username: 'qc_user', name: 'QC User', email: '<EMAIL>', role: ROLES.QC, active: true },
    { id: 4, username: 'logistics_user', name: 'Logistics User', email: '<EMAIL>', role: ROLES.LOGISTICS, active: true },
    { id: 6, username: 'user1', name: 'Jane Smith', email: '<EMAIL>', role: ROLES.STORE, active: true },
    { id: 7, username: 'user2', name: 'Bob Johnson', email: '<EMAIL>', role: ROLES.PRODUCTION, active: false },
  ]);

  // State for editing a user
  const [editingUser, setEditingUser] = useState(null);
  
  // State for creating a new user
  const [newUser, setNewUser] = useState({
    username: '',
    name: '',
    email: '',
    password: '',
    role: ROLES.STORE,
    active: true
  });
  
  // State for showing modals
  const [showNewUserModal, setShowNewUserModal] = useState(false);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  
  const [userProfile, setUserProfile] = useState({
    name: user?.name || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [systemConfig, setSystemConfig] = useState({
    maxBatchSize: config.settings.maxBatchSize || 50,
    defaultPaginationLimit: config.settings.defaultPaginationLimit || 10,
    qrCodeSize: 200,
    qrErrorCorrection: 'M',
  });
  
  const [interfaceSettings, setInterfaceSettings] = useState({
    theme: 'light',
    sidebarCollapsed: false,
    tableCompactView: false,
    defaultDateFormat: 'MM/DD/YYYY',
  });
  
  const [notificationSettings, setNotificationSettings] = useState({
    enableNotifications: config.features.enableNotifications || true,
    notificationDuration: 5000, // milliseconds
    emailNotifications: false,
    soundAlerts: false,
  });
  
  const [exportSettings, setExportSettings] = useState({
    defaultExportFormat: 'csv',
    includeMetadata: true,
    includeHistory: true,
  });
  
  const [customFields, setCustomFields] = useState({
    cylinderTypes: ['Oxygen', 'Nitrogen', 'Helium', 'Carbon Dioxide', 'Argon'],
    cylinderSizes: ['Small', 'Medium', 'Large'],
    cylinderMaterials: ['Steel', 'Aluminum', 'Composite'],
  });
  
  // Handle tab switching
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };
  
  // Handle input changes for different sections
  const handleUserProfileChange = (e) => {
    const { name, value } = e.target;
    setUserProfile(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSystemConfigChange = (e) => {
    const { name, value } = e.target;
    const numberFields = ['maxBatchSize', 'defaultPaginationLimit', 'qrCodeSize'];
    const newValue = numberFields.includes(name) ? Number(value) : value;
    setSystemConfig(prev => ({ ...prev, [name]: newValue }));
  };
  
  const handleInterfaceChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    setInterfaceSettings(prev => ({ ...prev, [name]: newValue }));
  };
  
  const handleNotificationChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    setNotificationSettings(prev => ({ ...prev, [name]: newValue }));
  };
  
  const handleExportChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    setExportSettings(prev => ({ ...prev, [name]: newValue }));
  };
  
  // Handle adding custom options to lists
  const handleAddCustomOption = (field, newOption) => {
    if (newOption && !customFields[field].includes(newOption)) {
      setCustomFields(prev => ({
        ...prev,
        [field]: [...prev[field], newOption]
      }));
      return true;
    }
    return false;
  };
  
  // Handle removing custom options from lists
  const handleRemoveCustomOption = (field, optionIndex) => {
    setCustomFields(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== optionIndex)
    }));
  };
  
  // Handle user management functions
  const handleEditUser = (user) => {
    setEditingUser({...user});
    setShowEditUserModal(true);
  };
  
  const handleResetPassword = (user) => {
    setEditingUser({...user});
    setShowResetPasswordModal(true);
  };
  
  const handleSaveUserEdit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // In a real app, you would call an API to update the user
      setTimeout(() => {
        setManagedUsers(prev => 
          prev.map(u => u.id === editingUser.id ? editingUser : u)
        );
        setShowEditUserModal(false);
        addNotification(`User ${editingUser.name} updated successfully`, 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating user: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleCreateUser = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Validate fields
      if (!newUser.username || !newUser.name || !newUser.email || !newUser.password) {
        addNotification('All fields are required', 'error');
        hideLoading();
        return;
      }
      
      // In a real app, you would call an API to create the user
      setTimeout(() => {
        const newUserId = Math.max(...managedUsers.map(u => u.id)) + 1;
        setManagedUsers(prev => [...prev, { ...newUser, id: newUserId }]);
        setShowNewUserModal(false);
        setNewUser({
          username: '',
          name: '',
          email: '',
          password: '',
          role: ROLES.STORE,
          active: true
        });
        addNotification('New user created successfully', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error creating user: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleResetUserPassword = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // In a real app, you would call an API to reset the password
      setTimeout(() => {
        setShowResetPasswordModal(false);
        addNotification(`Password reset for ${editingUser.name}`, 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error resetting password: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleToggleUserStatus = (userId) => {
    setManagedUsers(prev => 
      prev.map(u => u.id === userId ? { ...u, active: !u.active } : u)
    );
    
    const user = managedUsers.find(u => u.id === userId);
    const newStatus = !user.active;
    addNotification(`User ${user.name} ${newStatus ? 'activated' : 'deactivated'}`, 'info');
  };
  
  // Handle form submissions
  const handleProfileSubmit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Password validation
      if (userProfile.newPassword && userProfile.newPassword !== userProfile.confirmPassword) {
        addNotification('New passwords do not match', 'error');
        return;
      }
      
      // Here you would typically call an API to update the user profile
      // For demo purposes, we'll just simulate a successful update
      setTimeout(() => {
        updateUser({
          ...user,
          name: userProfile.name,
          email: userProfile.email,
        });
        
        addNotification('Profile updated successfully', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating profile: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleSystemConfigSubmit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Here you would typically update the system configuration
      setTimeout(() => {
        // In a real app, these values would be saved to a database or config store
        // For now, we'll just show a success message
        addNotification('System configuration updated', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating configuration: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleInterfaceSubmit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Save interface settings 
      setTimeout(() => {
        addNotification('Interface settings updated', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating interface settings: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleNotificationSubmit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Save notification settings
      setTimeout(() => {
        addNotification('Notification settings updated', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating notification settings: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleExportSubmit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Save export settings
      setTimeout(() => {
        addNotification('Export settings updated', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating export settings: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  const handleCustomFieldsSubmit = (e) => {
    e.preventDefault();
    showLoading();
    
    try {
      // Save custom fields
      setTimeout(() => {
        addNotification('Custom fields updated', 'success');
        hideLoading();
      }, 800);
    } catch (error) {
      addNotification(`Error updating custom fields: ${error.message}`, 'error');
      hideLoading();
    }
  };
  
  // Reset all settings to default
  const handleResetToDefaults = () => {
    // Ask for confirmation
    if (window.confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
      // Reset specific settings based on active tab
      switch (activeTab) {
        case 'profile':
          setUserProfile({
            name: user?.name || '',
            email: user?.email || '',
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          });
          break;
        case 'system':
          setSystemConfig({
            maxBatchSize: 50,
            defaultPaginationLimit: 10,
            qrCodeSize: 200,
            qrErrorCorrection: 'M',
          });
          break;
        case 'interface':
          setInterfaceSettings({
            theme: 'light',
            sidebarCollapsed: false,
            tableCompactView: false,
            defaultDateFormat: 'MM/DD/YYYY',
          });
          break;
        case 'notifications':
          setNotificationSettings({
            enableNotifications: true,
            notificationDuration: 5000,
            emailNotifications: false,
            soundAlerts: false,
          });
          break;
        case 'export':
          setExportSettings({
            defaultExportFormat: 'csv',
            includeMetadata: true,
            includeHistory: true,
          });
          break;
        case 'customFields':
          setCustomFields({
            cylinderTypes: ['Oxygen', 'Nitrogen', 'Helium', 'Carbon Dioxide', 'Argon'],
            cylinderSizes: ['Small', 'Medium', 'Large'],
            cylinderMaterials: ['Steel', 'Aluminum', 'Composite'],
          });
          break;
        default:
          break;
      }
      
      addNotification('Settings reset to default values', 'info');
    }
  };
  
  // Render the settings view with tabs
  return (
    <div className="space-y-6 pb-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Settings</h1>
          <p className="text-secondary-600">Configure your application preferences</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="md:col-span-1">
          <div className="card overflow-hidden">
            <div className="p-4 border-b border-secondary-200 bg-secondary-50">
              <h2 className="font-medium text-secondary-900">Settings Menu</h2>
            </div>
            <div className="p-2">
              <nav>
                <ul className="space-y-1">
                  <li>
                    <button
                      className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'profile' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                      onClick={() => handleTabChange('profile')}
                    >
                      <User className="h-5 w-5" />
                      <span>User Profile</span>
                    </button>
                  </li>
                  
                  {/* Only show User Management for admin users */}
                  {isAdmin && (
                    <li>
                      <button
                        className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'users' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                        onClick={() => handleTabChange('users')}
                      >
                        <Users className="h-5 w-5" />
                        <span>User Management</span>
                      </button>
                    </li>
                  )}
                  
                  <li>
                    <button
                      className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'system' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                      onClick={() => handleTabChange('system')}
                    >
                      <Sliders className="h-5 w-5" />
                      <span>System Configuration</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'interface' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                      onClick={() => handleTabChange('interface')}
                    >
                      <Paintbrush className="h-5 w-5" />
                      <span>Interface</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'notifications' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                      onClick={() => handleTabChange('notifications')}
                    >
                      <Bell className="h-5 w-5" />
                      <span>Notifications</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'export' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                      onClick={() => handleTabChange('export')}
                    >
                      <FileText className="h-5 w-5" />
                      <span>Import/Export</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className={`w-full flex items-center gap-3 px-4 py-2 rounded-md transition-colors ${activeTab === 'customFields' ? 'bg-primary-50 text-primary-700' : 'hover:bg-secondary-50'}`}
                      onClick={() => handleTabChange('customFields')}
                    >
                      <Database className="h-5 w-5" />
                      <span>Custom Fields</span>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
        
        {/* Settings Content */}
        <div className="md:col-span-3">
          <div className="card overflow-hidden">
            {/* Profile Settings */}
            {activeTab === 'profile' && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">User Profile</h2>
                  <button 
                    onClick={handleResetToDefaults}
                    className="text-secondary-500 hover:text-secondary-700 text-sm"
                  >
                    Reset to Default
                  </button>
                </div>
                <div className="p-6">
                  <form onSubmit={handleProfileSubmit}>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="label" htmlFor="name">
                            Full Name
                          </label>
                          <input
                            id="name"
                            name="name"
                            type="text"
                            className="input"
                            value={userProfile.name}
                            onChange={handleUserProfileChange}
                            placeholder="Enter your full name"
                            required
                          />
                        </div>
                        <div>
                          <label className="label" htmlFor="email">
                            Email Address
                          </label>
                          <input
                            id="email"
                            name="email"
                            type="email"
                            className="input"
                            value={userProfile.email}
                            onChange={handleUserProfileChange}
                            placeholder="Enter your email"
                          />
                        </div>
                      </div>
                      
                      <div className="border-t border-secondary-200 pt-6">
                        <h3 className="font-medium text-secondary-900 mb-4">Change Password</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="label" htmlFor="currentPassword">
                              Current Password
                            </label>
                            <input
                              id="currentPassword"
                              name="currentPassword"
                              type="password"
                              className="input"
                              value={userProfile.currentPassword}
                              onChange={handleUserProfileChange}
                              placeholder="Enter current password"
                            />
                          </div>
                          <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <label className="label" htmlFor="newPassword">
                                New Password
                              </label>
                              <input
                                id="newPassword"
                                name="newPassword"
                                type="password"
                                className="input"
                                value={userProfile.newPassword}
                                onChange={handleUserProfileChange}
                                placeholder="Enter new password"
                              />
                            </div>
                            <div>
                              <label className="label" htmlFor="confirmPassword">
                                Confirm Password
                              </label>
                              <input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                className="input"
                                value={userProfile.confirmPassword}
                                onChange={handleUserProfileChange}
                                placeholder="Confirm new password"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn-primary flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save Profile</span>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </>
            )}
            
            {/* User Management (Admin Only) */}
            {activeTab === 'users' && isAdmin && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">User Management</h2>
                  <button 
                    onClick={() => setShowNewUserModal(true)}
                    className="btn-primary flex items-center gap-2 py-1.5 px-3"
                  >
                    <UserPlus className="h-4 w-4" />
                    <span>Add New User</span>
                  </button>
                </div>
                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-secondary-200">
                      <thead className="bg-secondary-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">User</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Role</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Status</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-secondary-200">
                        {managedUsers.map((managedUser) => (
                          <tr key={managedUser.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 bg-secondary-200 rounded-full flex items-center justify-center">
                                  <User className="h-6 w-6 text-secondary-600" />
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-secondary-900">{managedUser.name}</div>
                                  <div className="text-sm text-secondary-500">{managedUser.email}</div>
                                  <div className="text-xs text-secondary-400">Username: {managedUser.username}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {managedUser.role}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${managedUser.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                {managedUser.active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex gap-2">
                                <button 
                                  onClick={() => handleEditUser(managedUser)} 
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  Edit
                                </button>
                                <button 
                                  onClick={() => handleResetPassword(managedUser)} 
                                  className="text-blue-600 hover:text-blue-900"
                                >
                                  Reset Password
                                </button>
                                <button 
                                  onClick={() => handleToggleUserStatus(managedUser.id)} 
                                  className={managedUser.active ? "text-red-600 hover:text-red-900" : "text-green-600 hover:text-green-900"}
                                >
                                  {managedUser.active ? 'Deactivate' : 'Activate'}
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            )}
            
            {/* System Configuration Settings */}
            {activeTab === 'system' && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">System Configuration</h2>
                  <button 
                    onClick={handleResetToDefaults}
                    className="text-secondary-500 hover:text-secondary-700 text-sm"
                  >
                    Reset to Default
                  </button>
                </div>
                <div className="p-6">
                  <form onSubmit={handleSystemConfigSubmit}>
                    <div className="space-y-6">
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">Batch Processing</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="label" htmlFor="maxBatchSize">
                              Maximum Batch Size
                            </label>
                            <input
                              id="maxBatchSize"
                              name="maxBatchSize"
                              type="number"
                              min="1"
                              max="100"
                              className="input"
                              value={systemConfig.maxBatchSize}
                              onChange={handleSystemConfigChange}
                              required
                            />
                            <p className="text-xs text-secondary-500 mt-1">Maximum number of cylinders that can be registered in a single batch</p>
                          </div>
                          <div>
                            <label className="label" htmlFor="defaultPaginationLimit">
                              Default Pagination Limit
                            </label>
                            <input
                              id="defaultPaginationLimit"
                              name="defaultPaginationLimit"
                              type="number"
                              min="5"
                              max="100"
                              className="input"
                              value={systemConfig.defaultPaginationLimit}
                              onChange={handleSystemConfigChange}
                              required
                            />
                            <p className="text-xs text-secondary-500 mt-1">Number of items to display per page in tables</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="border-t border-secondary-200 pt-6">
                        <h3 className="font-medium text-secondary-900 mb-4">QR Code Settings</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="label" htmlFor="qrCodeSize">
                              QR Code Size (pixels)
                            </label>
                            <input
                              id="qrCodeSize"
                              name="qrCodeSize"
                              type="number"
                              min="100"
                              max="500"
                              step="10"
                              className="input"
                              value={systemConfig.qrCodeSize}
                              onChange={handleSystemConfigChange}
                              required
                            />
                            <p className="text-xs text-secondary-500 mt-1">Size of generated QR codes for display and printing</p>
                          </div>
                          <div>
                            <label className="label" htmlFor="qrErrorCorrection">
                              Error Correction Level
                            </label>
                            <select
                              id="qrErrorCorrection"
                              name="qrErrorCorrection"
                              className="select"
                              value={systemConfig.qrErrorCorrection}
                              onChange={handleSystemConfigChange}
                              required
                            >
                              <option value="L">Low (7%)</option>
                              <option value="M">Medium (15%)</option>
                              <option value="Q">Quartile (25%)</option>
                              <option value="H">High (30%)</option>
                            </select>
                            <p className="text-xs text-secondary-500 mt-1">Higher error correction allows QR codes to be read even when partially damaged</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn-primary flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save Configuration</span>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </>
            )}
            
            {/* Interface Settings */}
            {activeTab === 'interface' && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">Interface Customization</h2>
                  <button 
                    onClick={handleResetToDefaults}
                    className="text-secondary-500 hover:text-secondary-700 text-sm"
                  >
                    Reset to Default
                  </button>
                </div>
                <div className="p-6">
                  <form onSubmit={handleInterfaceSubmit}>
                    <div className="space-y-6">
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">Theme Settings</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="label" htmlFor="theme">
                              Application Theme
                            </label>
                            <select
                              id="theme"
                              name="theme"
                              className="select"
                              value={interfaceSettings.theme}
                              onChange={handleInterfaceChange}
                              required
                            >
                              <option value="light">Light Mode</option>
                              <option value="dark">Dark Mode</option>
                              <option value="system">Follow System Preference</option>
                            </select>
                          </div>
                          <div className="flex items-center space-x-4">
                            <input
                              id="sidebarCollapsed"
                              name="sidebarCollapsed"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={interfaceSettings.sidebarCollapsed}
                              onChange={handleInterfaceChange}
                            />
                            <label className="label mb-0" htmlFor="sidebarCollapsed">
                              Collapse sidebar by default
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      <div className="border-t border-secondary-200 pt-6">
                        <h3 className="font-medium text-secondary-900 mb-4">Table Display Options</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="flex items-center space-x-4">
                            <input
                              id="tableCompactView"
                              name="tableCompactView"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={interfaceSettings.tableCompactView}
                              onChange={handleInterfaceChange}
                            />
                            <label className="label mb-0" htmlFor="tableCompactView">
                              Use compact table view
                            </label>
                          </div>
                          <div>
                            <label className="label" htmlFor="defaultDateFormat">
                              Default Date Format
                            </label>
                            <select
                              id="defaultDateFormat"
                              name="defaultDateFormat"
                              className="select"
                              value={interfaceSettings.defaultDateFormat}
                              onChange={handleInterfaceChange}
                              required
                            >
                              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                              <option value="MMMM D, YYYY">MMMM D, YYYY</option>
                            </select>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn-primary flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save Interface Settings</span>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </>
            )}
            
            {/* Notification Settings */}
            {activeTab === 'notifications' && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">Notification Settings</h2>
                  <button 
                    onClick={handleResetToDefaults}
                    className="text-secondary-500 hover:text-secondary-700 text-sm"
                  >
                    Reset to Default
                  </button>
                </div>
                <div className="p-6">
                  <form onSubmit={handleNotificationSubmit}>
                    <div className="space-y-6">
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">In-App Notifications</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="flex items-center space-x-4">
                            <input
                              id="enableNotifications"
                              name="enableNotifications"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={notificationSettings.enableNotifications}
                              onChange={handleNotificationChange}
                            />
                            <label className="label mb-0" htmlFor="enableNotifications">
                              Enable in-app notifications
                            </label>
                          </div>
                          <div>
                            <label className="label" htmlFor="notificationDuration">
                              Notification Duration (ms)
                            </label>
                            <select
                              id="notificationDuration"
                              name="notificationDuration"
                              className="select"
                              value={notificationSettings.notificationDuration}
                              onChange={handleNotificationChange}
                              required
                            >
                              <option value="3000">3 seconds</option>
                              <option value="5000">5 seconds</option>
                              <option value="8000">8 seconds</option>
                              <option value="10000">10 seconds</option>
                              <option value="0">Don't auto-dismiss</option>
                            </select>
                          </div>
                        </div>
                      </div>
                      
                      <div className="border-t border-secondary-200 pt-6">
                        <h3 className="font-medium text-secondary-900 mb-4">Other Notifications</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="flex items-center space-x-4">
                            <input
                              id="emailNotifications"
                              name="emailNotifications"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={notificationSettings.emailNotifications}
                              onChange={handleNotificationChange}
                            />
                            <label className="label mb-0" htmlFor="emailNotifications">
                              Enable email notifications
                            </label>
                          </div>
                          <div className="flex items-center space-x-4">
                            <input
                              id="soundAlerts"
                              name="soundAlerts"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={notificationSettings.soundAlerts}
                              onChange={handleNotificationChange}
                            />
                            <label className="label mb-0" htmlFor="soundAlerts">
                              Enable sound alerts
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn-primary flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save Notification Settings</span>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </>
            )}
            
            {/* Export Settings */}
            {activeTab === 'export' && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">Import/Export Settings</h2>
                  <button 
                    onClick={handleResetToDefaults}
                    className="text-secondary-500 hover:text-secondary-700 text-sm"
                  >
                    Reset to Default
                  </button>
                </div>
                <div className="p-6">
                  <form onSubmit={handleExportSubmit}>
                    <div className="space-y-6">
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">Export Options</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="label" htmlFor="defaultExportFormat">
                              Default Export Format
                            </label>
                            <select
                              id="defaultExportFormat"
                              name="defaultExportFormat"
                              className="select"
                              value={exportSettings.defaultExportFormat}
                              onChange={handleExportChange}
                              required
                            >
                              <option value="csv">CSV (.csv)</option>
                              <option value="excel">Excel (.xlsx)</option>
                              <option value="pdf">PDF (.pdf)</option>
                              <option value="json">JSON (.json)</option>
                            </select>
                          </div>
                          <div className="flex items-center space-x-4">
                            <input
                              id="includeMetadata"
                              name="includeMetadata"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={exportSettings.includeMetadata}
                              onChange={handleExportChange}
                            />
                            <label className="label mb-0" htmlFor="includeMetadata">
                              Include metadata in exports
                            </label>
                          </div>
                          <div className="flex items-center space-x-4">
                            <input
                              id="includeHistory"
                              name="includeHistory"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                              checked={exportSettings.includeHistory}
                              onChange={handleExportChange}
                            />
                            <label className="label mb-0" htmlFor="includeHistory">
                              Include history in exports
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn-primary flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save Export Settings</span>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </>
            )}
            
            {/* Custom Fields */}
            {activeTab === 'customFields' && (
              <>
                <div className="p-6 border-b border-secondary-200 flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-secondary-900">Custom Fields</h2>
                  <button 
                    onClick={handleResetToDefaults}
                    className="text-secondary-500 hover:text-secondary-700 text-sm"
                  >
                    Reset to Default
                  </button>
                </div>
                <div className="p-6">
                  <form onSubmit={handleCustomFieldsSubmit}>
                    <div className="space-y-8">
                      {/* Cylinder Types */}
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">Cylinder Types</h3>
                        <div className="border rounded-lg p-4 bg-secondary-50">
                          <div className="flex flex-wrap gap-2 mb-4">
                            {customFields.cylinderTypes.map((type, index) => (
                              <div 
                                key={index}
                                className="bg-white px-3 py-1 rounded-full border flex items-center gap-1"
                              >
                                <span className="text-sm">{type}</span>
                                <button 
                                  type="button"
                                  className="text-secondary-400 hover:text-secondary-600"
                                  onClick={() => handleRemoveCustomOption('cylinderTypes', index)}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                          <div className="flex gap-2">
                            <input
                              type="text"
                              id="newCylinderType"
                              className="input text-sm flex-1"
                              placeholder="Add new cylinder type"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  const success = handleAddCustomOption('cylinderTypes', e.target.value.trim());
                                  if (success) e.target.value = '';
                                }
                              }}
                            />
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={(e) => {
                                const input = document.getElementById('newCylinderType');
                                const success = handleAddCustomOption('cylinderTypes', input.value.trim());
                                if (success) input.value = '';
                              }}
                            >
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      {/* Cylinder Sizes */}
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">Cylinder Sizes</h3>
                        <div className="border rounded-lg p-4 bg-secondary-50">
                          <div className="flex flex-wrap gap-2 mb-4">
                            {customFields.cylinderSizes.map((size, index) => (
                              <div 
                                key={index}
                                className="bg-white px-3 py-1 rounded-full border flex items-center gap-1"
                              >
                                <span className="text-sm">{size}</span>
                                <button 
                                  type="button"
                                  className="text-secondary-400 hover:text-secondary-600"
                                  onClick={() => handleRemoveCustomOption('cylinderSizes', index)}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                          <div className="flex gap-2">
                            <input
                              type="text"
                              id="newCylinderSize"
                              className="input text-sm flex-1"
                              placeholder="Add new cylinder size"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  const success = handleAddCustomOption('cylinderSizes', e.target.value.trim());
                                  if (success) e.target.value = '';
                                }
                              }}
                            />
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={(e) => {
                                const input = document.getElementById('newCylinderSize');
                                const success = handleAddCustomOption('cylinderSizes', input.value.trim());
                                if (success) input.value = '';
                              }}
                            >
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      {/* Cylinder Materials */}
                      <div>
                        <h3 className="font-medium text-secondary-900 mb-4">Cylinder Materials</h3>
                        <div className="border rounded-lg p-4 bg-secondary-50">
                          <div className="flex flex-wrap gap-2 mb-4">
                            {customFields.cylinderMaterials.map((material, index) => (
                              <div 
                                key={index}
                                className="bg-white px-3 py-1 rounded-full border flex items-center gap-1"
                              >
                                <span className="text-sm">{material}</span>
                                <button 
                                  type="button"
                                  className="text-secondary-400 hover:text-secondary-600"
                                  onClick={() => handleRemoveCustomOption('cylinderMaterials', index)}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                          <div className="flex gap-2">
                            <input
                              type="text"
                              id="newCylinderMaterial"
                              className="input text-sm flex-1"
                              placeholder="Add new material"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  const success = handleAddCustomOption('cylinderMaterials', e.target.value.trim());
                                  if (success) e.target.value = '';
                                }
                              }}
                            />
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={(e) => {
                                const input = document.getElementById('newCylinderMaterial');
                                const success = handleAddCustomOption('cylinderMaterials', input.value.trim());
                                if (success) input.value = '';
                              }}
                            >
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn-primary flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save Custom Fields</span>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Modal for editing a user */}
      {showEditUserModal && editingUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div className="flex items-center justify-between p-6 border-b border-secondary-200">
              <h3 className="text-lg font-medium text-secondary-900">Edit User</h3>
              <button 
                onClick={() => setShowEditUserModal(false)} 
                className="text-secondary-400 hover:text-secondary-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <form onSubmit={handleSaveUserEdit}>
              <div className="p-6 space-y-4">
                <div>
                  <label className="label" htmlFor="editName">Name</label>
                  <input 
                    id="editName"
                    type="text" 
                    className="input" 
                    value={editingUser.name} 
                    onChange={(e) => setEditingUser({...editingUser, name: e.target.value})} 
                    required
                  />
                </div>
                <div>
                  <label className="label" htmlFor="editEmail">Email</label>
                  <input 
                    id="editEmail"
                    type="email" 
                    className="input" 
                    value={editingUser.email} 
                    onChange={(e) => setEditingUser({...editingUser, email: e.target.value})} 
                    required
                  />
                </div>
                <div>
                  <label className="label" htmlFor="editRole">Role</label>
                  <select 
                    id="editRole"
                    className="select" 
                    value={editingUser.role} 
                    onChange={(e) => setEditingUser({...editingUser, role: e.target.value})}
                  >
                    <option value={ROLES.ADMIN}>Admin</option>
                    <option value={ROLES.STORE}>Store</option>
                    <option value={ROLES.PRODUCTION}>Production</option>
                    <option value={ROLES.QC}>Quality Control</option>
                    <option value={ROLES.LOGISTICS}>Logistics</option>
                  </select>
                </div>
                <div className="flex items-center">
                  <input 
                    id="editActive"
                    type="checkbox" 
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                    checked={editingUser.active}
                    onChange={(e) => setEditingUser({...editingUser, active: e.target.checked})}
                  />
                  <label htmlFor="editActive" className="ml-2 block text-sm text-secondary-900">
                    Active
                  </label>
                </div>
              </div>
              <div className="px-6 py-4 bg-secondary-50 text-right">
                <button
                  type="button"
                  className="btn-secondary mr-2"
                  onClick={() => setShowEditUserModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Modal for resetting a password */}
      {showResetPasswordModal && editingUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b border-secondary-200">
              <h3 className="text-lg font-medium text-secondary-900">Reset Password for {editingUser.name}</h3>
              <button 
                onClick={() => setShowResetPasswordModal(false)} 
                className="text-secondary-400 hover:text-secondary-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <form onSubmit={handleResetUserPassword}>
              <div className="p-6 space-y-4">
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Shield className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        This will reset the password for {editingUser.name}. They will need to change it after login.
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="label" htmlFor="newUserPassword">New Password</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Key className="h-5 w-5 text-secondary-400" />
                    </div>
                    <input 
                      id="newUserPassword"
                      type="password" 
                      className="input pl-10" 
                      placeholder="Enter new password" 
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="px-6 py-4 bg-secondary-50 text-right">
                <button
                  type="button"
                  className="btn-secondary mr-2"
                  onClick={() => setShowResetPasswordModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  Reset Password
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Modal for creating a new user */}
      {showNewUserModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div className="flex items-center justify-between p-6 border-b border-secondary-200">
              <h3 className="text-lg font-medium text-secondary-900">Create New User</h3>
              <button 
                onClick={() => setShowNewUserModal(false)} 
                className="text-secondary-400 hover:text-secondary-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <form onSubmit={handleCreateUser}>
              <div className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="label" htmlFor="newUserName">Full Name</label>
                    <input 
                      id="newUserName"
                      type="text" 
                      className="input" 
                      value={newUser.name} 
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})} 
                      placeholder="Enter full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="label" htmlFor="newUsername">Username</label>
                    <input 
                      id="newUsername"
                      type="text" 
                      className="input" 
                      value={newUser.username} 
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})} 
                      placeholder="Enter username"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="label" htmlFor="newUserEmail">Email</label>
                  <input 
                    id="newUserEmail"
                    type="email" 
                    className="input" 
                    value={newUser.email} 
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})} 
                    placeholder="Enter email address"
                    required
                  />
                </div>
                <div>
                  <label className="label" htmlFor="newUserPassword">Password</label>
                  <input 
                    id="newUserPassword"
                    type="password" 
                    className="input" 
                    value={newUser.password} 
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})} 
                    placeholder="Enter password"
                    required
                  />
                </div>
                <div>
                  <label className="label" htmlFor="newUserRole">Role</label>
                  <select 
                    id="newUserRole"
                    className="select" 
                    value={newUser.role} 
                    onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                  >
                    <option value={ROLES.ADMIN}>Admin</option>
                    <option value={ROLES.STORE}>Store</option>
                    <option value={ROLES.PRODUCTION}>Production</option>
                    <option value={ROLES.QC}>Quality Control</option>
                    <option value={ROLES.LOGISTICS}>Logistics</option>
                  </select>
                </div>
                <div className="flex items-center">
                  <input 
                    id="newUserActive"
                    type="checkbox" 
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                    checked={newUser.active}
                    onChange={(e) => setNewUser({...newUser, active: e.target.checked})}
                  />
                  <label htmlFor="newUserActive" className="ml-2 block text-sm text-secondary-900">
                    Active
                  </label>
                </div>
              </div>
              <div className="px-6 py-4 bg-secondary-50 text-right">
                <button
                  type="button"
                  className="btn-secondary mr-2"
                  onClick={() => setShowNewUserModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  Create User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default SettingsView;