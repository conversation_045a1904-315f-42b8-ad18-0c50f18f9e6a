-- Function to automatically create user profile on signup
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION create_user_profile()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (id, email, name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'role', 'Store') -- Use role from metadata, default to 'Store'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Trigger to automatically create user profile on signup
CREATE OR REPLACE TRIGGER create_user_profile_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_user_profile();
